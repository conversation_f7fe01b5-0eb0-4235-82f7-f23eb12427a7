'use strict';

/**
 * 简化版抖音解析器 - 用于快速验证
 * 使用Node.js实现，避免Python环境配置问题
 */

exports.main = async (event, context) => {
  console.log('收到解析请求:', event);

  const { link, forceRemoveWatermark = false, debug = false, getCover = false } = event;

  if (!link) {
    return {
      success: false,
      message: '链接不能为空'
    };
  }

  try {
    // 检测是否为抖音链接
    if (!isDouyinLink(link)) {
      return {
        success: false,
        message: '仅支持抖音链接'
      };
    }

    // 如果只是获取封面，使用专门的封面获取逻辑
    if (getCover) {
      const coverResult = await getVideoCover(link);
      return {
        success: true,
        data: coverResult
      };
    }

    // 检查是否为slides类型的链接
    console.log('检查链接类型，link:', link);
    console.log('包含/share/slides/:', link.includes('/share/slides/'));
    
    if (link.includes('/share/slides/')) {
      console.log('检测到slides类型链接，使用特殊解析方法');
      const result = await parseSlidesContent(link);
      return {
        success: true,
        data: result
      };
    }

    // 使用自建解析
    console.log('使用标准解析方法');
    
    // 清理链接
    let cleanedLink = cleanLink(link);
    console.log('清理后的链接:', cleanedLink);
    
    const result = await parseDouyinVideo(cleanedLink, forceRemoveWatermark);

    return {
      success: true,
      data: result
    };

  } catch (error) {
    console.error('解析失败:', error);
    return {
      success: false,
      message: error.message || '解析失败'
    };
  }
};

function isDouyinLink(link) {
  return /douyin\.com|dy\.com/i.test(link);
}

// 递归查找对象中的desc字段
// 通用递归查找函数，合并desc/nickname/duration查找逻辑
function findFieldInObject(obj, targetField, alternativeFields = [], depth = 0) {
  if (!obj || typeof obj !== 'object' || depth > 8) {
    return null;
  }
  
  // 检查目标字段
  if (obj[targetField] && typeof obj[targetField] === 'string' && obj[targetField].trim()) {
    return obj[targetField];
  }
  
  // 检查备选字段
  for (const field of alternativeFields) {
    if (obj[field] && typeof obj[field] === 'string' && obj[field].trim()) {
      return obj[field];
    }
  }
  
  // 递归查找
  for (const key in obj) {
    if (obj.hasOwnProperty(key) && obj[key] && typeof obj[key] === 'object') {
      if (Array.isArray(obj[key])) {
        for (let i = 0; i < Math.min(obj[key].length, 3); i++) {
          const result = findFieldInObject(obj[key][i], targetField, alternativeFields, depth + 1);
          if (result) return result;
        }
      } else {
        const result = findFieldInObject(obj[key], targetField, alternativeFields, depth + 1);
        if (result) return result;
      }
    }
  }
  
  return null;
}

// 删除冗余的findFieldInObject函数，使用通用函数findFieldInObject替代

// 删除冗余的findFieldInObject函数，使用通用函数findFieldInObject替代

// 基于官方API标准分析内容类型
function analyzeContentType(html) {
  try {
    // 开始内容类型分析

    // 1. 检查aweme_type
    const awemeTypeMatch = html.match(/"aweme_type":(\d+)/);
    const awemeType = awemeTypeMatch ? parseInt(awemeTypeMatch[1]) : null;
    console.log('aweme_type:', awemeType);

    // 2. 检查其他关键标识
    const hasImages = html.includes('"images":[') && !html.includes('"images":[]');
    const hasVideo = html.includes('"video":{') || html.includes('play_addr');
    const hasPlayAddr = html.includes('play_addr');
    
    console.log('关键标识检查:');
    console.log('- 包含images数组:', hasImages);
    console.log('- 包含video对象:', hasVideo);
    console.log('- 包含play_addr:', hasPlayAddr);

    // 3. 搜索更多线索
    const imageKeywords = ['"images"', 'image_list', 'photo'];
    const videoKeywords = ['"video"', 'play_addr', 'video_list'];
    
    let imageScore = 0;
    let videoScore = 0;
    
    imageKeywords.forEach(keyword => {
      if (html.includes(keyword)) {
        imageScore++;
        console.log(`找到图片关键词: ${keyword}`);
      }
    });
    
    videoKeywords.forEach(keyword => {
      if (html.includes(keyword)) {
        videoScore++;
        console.log(`找到视频关键词: ${keyword}`);
      }
    });
    
    console.log(`评分 - 图片:${imageScore}, 视频:${videoScore}`);

    // 4. 基于aweme_type的官方判断
    if (awemeType === 2 || awemeType === 150) {
      console.log(`官方类型${awemeType} -> 图文内容`);
      return 'image';
    } else if (awemeType === 4 || awemeType === 68) {
      console.log(`官方类型${awemeType} -> 视频内容`);
      return 'video';
    }
    
    // 5. 基于内容特征的推断
    if (hasImages && !hasPlayAddr) {
      console.log('有图片无视频播放地址 -> 图文内容');
      return 'image';
    } else if (hasPlayAddr) {
      console.log('有播放地址 -> 视频内容');
      return 'video';
    }
    
    console.log('无法确定类型，默认为视频');
    // 内容类型分析结束
    return 'video';

  } catch (error) {
    console.error('内容类型分析失败:', error);
    return 'unknown';
  }
}

// 🚀 专门解析slides SSR页面
async function parseSlidesSSRPage(shareUrl, realUrl, pageContent) {
  // 开始解析slides SSR页面
  
  try {
    // 提取slides ID
    const slidesIdMatch = realUrl.match(/\/slides\/(\d+)/);
    if (!slidesIdMatch) {
      throw new Error('无法从URL提取slides ID');
    }
    
    const slidesId = slidesIdMatch[1];
    console.log('提取到slides ID:', slidesId);
    
    // 策略1：尝试API接口获取数据
    console.log('📡 策略1：尝试API接口');
    const apiResult = await fetchSlidesDataFromAPI(slidesId);
    if (apiResult) {
      console.log('✅ API接口获取成功');
      return apiResult;
    }
    
    // 策略2：构建不同的URL格式进行请求
    console.log('🔄 策略2：尝试不同URL格式');
    const urlVariants = [
      `https://www.iesdouyin.com/share/note/${slidesId}`,
      `https://www.iesdouyin.com/share/video/${slidesId}`, 
      `https://www.douyin.com/note/${slidesId}`,
      `https://www.douyin.com/video/${slidesId}`
    ];
    
    for (let i = 0; i < urlVariants.length; i++) {
      const testUrl = urlVariants[i];
      console.log(`尝试URL ${i + 1}: ${testUrl}`);
      
      try {
        const content = await getPageContent(testUrl);
        console.log(`URL ${i + 1} 响应长度:`, content.length);
        
        // 检查是否包含有效数据
        if (content.includes('aweme_type') || content.includes('"images"') || content.includes('play_addr')) {
          console.log(`✅ URL ${i + 1} 包含有效数据，开始解析`);
          
          const contentType = analyzeContentType(content);
          console.log('解析内容类型:', contentType);
          
          if (contentType === 'image') {
            const imageInfo = await extractImageInfo(content, shareUrl);
            
            // 🆕 处理背景视频（如果存在）
            let backgroundVideo = null;
            if (imageInfo.backgroundVideoUrl) {
              console.log('🎬 处理图文背景视频');
              try {
                backgroundVideo = await getNoWatermarkVideo(imageInfo.backgroundVideoUrl);
                console.log('✅ 背景视频处理完成:', backgroundVideo);
              } catch (videoError) {
                console.log('❌ 背景视频处理失败:', videoError.message);
              }
            }
            
            return {
              title: imageInfo.title,
              author: imageInfo.author,
              processedData: {
                data: imageInfo.videoUrl, // 主图片
                type: 'image/jpeg',
                isUrl: true,
                imageUrls: imageInfo.imageUrls, // 所有图片
                isImageContent: true,
                // 🆕 为每张图片都提供相同的背景视频（如小红书Live Photo）
                videoUrls: backgroundVideo ? 
                  new Array(imageInfo.imageUrls.length).fill(backgroundVideo) : [],
                hasBackgroundVideo: !!backgroundVideo,
                rawBackgroundVideoUrl: imageInfo.backgroundVideoUrl // 原始背景视频URL
              },
              type: 'image', // 主要类型还是图文
              platform: 'douyin',
              source: '抖音',
              note: backgroundVideo ? 
                `已获取动态图文内容（SSR解析，共${imageInfo.imageUrls.length}张图片+背景视频）` :
                `已获取图文内容（SSR解析，共${imageInfo.imageUrls.length}张图片）`,
              originalUrl: shareUrl
            };
          } else {
            const videoInfo = await extractVideoInfo(content);
            const processedUrl = await getNoWatermarkVideo(videoInfo.videoUrl);
            
            return {
              title: videoInfo.title,
              author: videoInfo.author,
              processedData: {
                data: processedUrl,
                type: 'video/mp4',
                isUrl: true,
                isDirectUrl: !processedUrl.includes('aweme.snssdk.com')
              },
              type: 'video',
              platform: 'douyin',
              source: '抖音',
              note: '已获取内容（SSR解析）',
              originalUrl: shareUrl
            };
          }
        } else {
          console.log(`❌ URL ${i + 1} 不包含有效数据`);
        }
      } catch (error) {
        console.log(`❌ URL ${i + 1} 请求失败:`, error.message);
        continue;
      }
    }
    
    console.log('❌ 所有策略都失败');
    throw new Error('SSR页面解析失败，无法获取内容数据');
    
  } catch (error) {
    console.error('解析slides SSR页面失败:', error);
    throw error;
  }
}

// 专门处理slides类型链接的函数
async function parseSlidesContent(shareUrl) {
  console.log('开始解析slides类型内容:', shareUrl);

  try {
    // 提取slides ID
    const slidesIdMatch = shareUrl.match(/\/slides\/(\d+)/);
    if (!slidesIdMatch) {
      throw new Error('无法从slides链接中提取ID');
    }
    
    const slidesId = slidesIdMatch[1];
    console.log('提取到slides ID:', slidesId);

    // 对于slides链接，尝试基于PHP代码的思路：使用 /share/video/ 而不是 /share/slides/
    console.log('尝试方法1：转换为share/video链接获取_ROUTER_DATA');
    const videoUrl = `https://www.iesdouyin.com/share/video/${slidesId}`;
    console.log('构建的video URL:', videoUrl);
    
    try {
      // 获取video页面的内容，查找 window._ROUTER_DATA
      const videoPageContent = await getPageContent(videoUrl);
      console.log('video页面获取成功，长度:', videoPageContent.length);
      
      // 检查是否包含 _ROUTER_DATA
      if (videoPageContent.includes('_ROUTER_DATA')) {
        console.log('找到_ROUTER_DATA，开始解析');
        const routerDataResult = parseRouterData(videoPageContent, slidesId, shareUrl);
        if (routerDataResult) {
          return routerDataResult;
        }
      } else {
        console.log('video页面中未找到_ROUTER_DATA');
      }
    } catch (videoError) {
      console.error('获取video页面失败:', videoError.message);
    }

    // 方法2：尝试通过API接口获取slides数据
    console.log('尝试方法2：API接口获取数据');
    const apiResult = await fetchSlidesDataFromAPI(slidesId);
    if (apiResult) {
      console.log('通过API成功获取slides数据');
      return apiResult;
    }

    // 方法3：尝试转换为note链接
    console.log('尝试方法3：转换为note链接');
    const noteUrl = shareUrl.replace('/share/slides/', '/share/note/');
    console.log('转换后的note链接:', noteUrl);
    
    try {
      // 使用标准的解析方法处理note链接
      const noteResult = await parseDouyinVideo(noteUrl, false);
      
      // 确保返回的是图文类型
      if (noteResult && noteResult.processedData) {
        noteResult.type = 'image';
        noteResult.processedData.isImageContent = true;
        noteResult.note = `已获取图文内容（slides转note成功）`;
        console.log('note链接转换成功');
        return noteResult;
      }
    } catch (noteError) {
      console.error('note链接转换失败:', noteError.message);
    }

    // 方法4：构建基于ID的直接API请求
    console.log('尝试方法4：基于ID的直接构建');
    const directResult = await buildSlidesContentFromId(slidesId, shareUrl);
    if (directResult) {
      return directResult;
    }

    // 如果所有方法都失败，返回一个基础结果
    console.log('所有解析方法都失败，返回基础结果');
    return {
      title: '抖音图文内容',
      author: '抖音用户',
      processedData: {
        data: `https://p3-sign.douyinpic.com/tos-cn-i-0813/placeholder.jpg`, // 占位图
        type: 'image/jpeg',
        isUrl: true,
        imageUrls: [],
        isImageContent: true
      },
      type: 'image',
      platform: 'douyin',
      source: '抖音',
      note: 'slides链接解析受限，请尝试使用note链接',
      originalUrl: shareUrl
    };

  } catch (error) {
    console.error('解析slides内容失败:', error);
    throw error;
  }
}

// 统一的API接口获取slides数据（合并重复代码）
async function fetchSlidesDataFromAPI(slidesId) {
  try {
    console.log('尝试通过API获取slides数据, ID:', slidesId);

    // 精简的API端点列表（删除重复）
    const apiUrls = [
      `https://www.iesdouyin.com/aweme/v1/web/aweme/detail/?aweme_id=${slidesId}&aid=1128`,
      `https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/?item_ids=${slidesId}`,
      `https://aweme.snssdk.com/aweme/v1/feed/?aweme_id=${slidesId}`
    ];

    for (const apiUrl of apiUrls) {
      try {
        console.log('尝试API地址:', apiUrl);
        
        // 生成更真实的请求头
        const headers = {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
          'Referer': 'https://www.douyin.com/',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        };
        
        // 为不同的API添加特定的请求头
        if (apiUrl.includes('iesdouyin.com')) {
          headers['X-Requested-With'] = 'XMLHttpRequest';
        }
        
        const response = await uniCloud.httpclient.request(apiUrl, {
          method: 'GET',
          timeout: 15000,
          headers: headers
        });

        if (response.status === 200 && response.data) {
          console.log('API请求成功，状态码:', response.status);
          console.log('响应内容长度:', response.data.length);
          
          let data;
          try {
            data = typeof response.data === 'string' ? JSON.parse(response.data) : response.data;
          } catch (jsonError) {
            console.log('JSON解析失败:', jsonError.message);
            console.log('响应内容前200字符:', response.data.substring(0, 200));
            continue;
          }
          
          // 打印响应结构以便调试
          console.log('API响应结构键:', Object.keys(data));
          
          // 解析API返回的数据
          const parsedResult = await parseAPIResponse(data, originalUrl);
          if (parsedResult) {
            return parsedResult;
          } else {
            console.log('API响应解析失败，数据结构可能不匹配');
          }
        } else {
          console.log('API请求失败，状态码:', response.status);
        }
      } catch (apiError) {
        console.log('API请求异常:', apiError.message);
        continue;
      }
    }

    return null;
  } catch (error) {
    console.error('API获取slides数据失败:', error);
    return null;
  }
}

// 解析API返回的数据
async function parseAPIResponse(data, originalUrl) {
  try {
    console.log('开始解析API响应数据');
    console.log('API响应根级别键:', Object.keys(data));
    
    // 查找aweme_list或item_list
    let awemeInfo = null;
    
    // 多种可能的数据结构
    if (data.aweme_list && data.aweme_list.length > 0) {
      awemeInfo = data.aweme_list[0];
      console.log('从aweme_list找到数据');
    } else if (data.item_list && data.item_list.length > 0) {
      awemeInfo = data.item_list[0];
      console.log('从item_list找到数据');
    } else if (data.aweme_detail) {
      awemeInfo = data.aweme_detail;
      console.log('从aweme_detail找到数据');
    } else if (data.aweme_info) {
      awemeInfo = data.aweme_info;
      console.log('从aweme_info找到数据');
    } else if (data.data && data.data.length > 0) {
      awemeInfo = data.data[0];
      console.log('从data数组找到数据');
    } else if (data.result && data.result.length > 0) {
      awemeInfo = data.result[0];
      console.log('从result数组找到数据');
    } else {
      // 尝试直接使用根级别数据
      if (data.aweme_id || data.desc || data.author) {
        awemeInfo = data;
        console.log('直接使用根级别数据');
      }
    }

    if (!awemeInfo) {
      console.log('API响应中未找到有效的aweme信息');
      console.log('可用的根级别字段:', Object.keys(data));
      return null;
    }

    console.log('找到aweme信息，字段包括:', Object.keys(awemeInfo));

    // 提取基本信息 - 尝试多种字段名
    const title = awemeInfo.desc || 
                 awemeInfo.content || 
                 awemeInfo.share_info?.share_title || 
                 awemeInfo.share_info?.share_desc ||
                 awemeInfo.statistics?.share_title ||
                 '图文内容';
                 
    const author = awemeInfo.author?.nickname || 
                  awemeInfo.author?.unique_id || 
                  awemeInfo.author?.short_id ||
                  awemeInfo.nickname ||
                  '未知作者';

    console.log('提取到标题:', title);
    console.log('提取到作者:', author);

    // 提取图片信息和Live Photo视频 - 尝试多种结构
    let imageUrls = [];
    let videoUrls = []; // 新增：Live Photo背景视频URL数组
    
    // 方法1：从images字段提取图片和对应的Live Photo视频
    if (awemeInfo.images && awemeInfo.images.length > 0) {
      console.log('找到images数组，长度:', awemeInfo.images.length);
      
      awemeInfo.images.forEach((img, index) => {
        // 提取静态图片URL
        let imageUrl = null;
        
        if (img.url_list && img.url_list.length > 0) {
          imageUrl = img.url_list[0];
        } else if (img.uri && typeof img.uri === 'string') {
          imageUrl = img.uri.startsWith('http') ? img.uri : `https://p3-sign.douyinpic.com/${img.uri}`;
        } else if (img.url) {
          imageUrl = img.url;
        } else if (typeof img === 'string' && img.startsWith('http')) {
          imageUrl = img;
        }
        
        if (imageUrl && imageUrl.startsWith('http')) {
          imageUrls.push(imageUrl);
          console.log(`提取第${index + 1}张图片:`, imageUrl);
        }
        
        // 🆕 新增：提取Live Photo背景视频URL
        let livePhotoVideoUrl = null;
        
        // 方法1：从video字段提取
        if (img.video && img.video.play_addr) {
          if (img.video.play_addr.url_list && img.video.play_addr.url_list.length > 0) {
            livePhotoVideoUrl = img.video.play_addr.url_list[0];
            console.log(`🎬 从images[${index}].video.play_addr找到Live Photo视频:`, livePhotoVideoUrl);
          }
        }
        
        // 方法2：检查是否有live_photo字段
        if (!livePhotoVideoUrl && img.live_photo) {
          if (img.live_photo.url_list && img.live_photo.url_list.length > 0) {
            livePhotoVideoUrl = img.live_photo.url_list[0];
            console.log(`🎬 从images[${index}].live_photo找到Live Photo视频:`, livePhotoVideoUrl);
          } else if (img.live_photo.uri) {
            // 尝试构建视频URL
            livePhotoVideoUrl = `https://v5-coldn.douyinvod.com/${img.live_photo.uri}`;
            console.log(`🎬 从images[${index}].live_photo.uri构建Live Photo视频:`, livePhotoVideoUrl);
          }
        }
        
        // 方法3：检查是否有play_url或video_url字段
        if (!livePhotoVideoUrl) {
          const videoFields = ['play_url', 'video_url', 'live_video', 'motion_video'];
          for (const field of videoFields) {
            if (img[field]) {
              if (img[field].url_list && img[field].url_list.length > 0) {
                livePhotoVideoUrl = img[field].url_list[0];
                console.log(`🎬 从images[${index}].${field}找到Live Photo视频:`, livePhotoVideoUrl);
                break;
              } else if (typeof img[field] === 'string' && img[field].includes('douyinvod.com')) {
                livePhotoVideoUrl = img[field];
                console.log(`🎬 从images[${index}].${field}字符串找到Live Photo视频:`, livePhotoVideoUrl);
                break;
              }
            }
          }
        }
        
        // 方法4：如果还没找到，尝试从img对象的其他字段查找包含douyinvod.com的URL
        if (!livePhotoVideoUrl) {
          const imgStr = JSON.stringify(img);
          const videoUrlMatch = imgStr.match(/https?:\/\/[^"]*douyinvod\.com[^"]*\.mp4[^"]*/);
          if (videoUrlMatch) {
            livePhotoVideoUrl = videoUrlMatch[0];
            console.log(`🎬 通过正则匹配在images[${index}]中找到Live Photo视频:`, livePhotoVideoUrl);
          }
        }
        
        // 将找到的视频URL添加到数组中
        if (livePhotoVideoUrl && livePhotoVideoUrl.startsWith('http')) {
          videoUrls.push(livePhotoVideoUrl);
          console.log(`✅ 第${index + 1}张图片对应的Live Photo视频已添加`);
        } else {
          // 如果没找到对应的视频，添加null占位
          videoUrls.push(null);
          console.log(`⚠️  第${index + 1}张图片没有找到对应的Live Photo视频`);
        }
      });
    }

    // 方法2：从video的cover或animated_cover提取（如果是图文视频）
    if (imageUrls.length === 0 && awemeInfo.video) {
      console.log('尝试从video对象提取图片');
      
      const videoObj = awemeInfo.video;
      const coverSources = [
        videoObj.cover?.url_list,
        videoObj.origin_cover?.url_list,
        videoObj.dynamic_cover?.url_list,
        videoObj.animated_cover?.url_list
      ];
      
      for (const source of coverSources) {
        if (source && source.length > 0) {
          const url = source[0];
          if (url && url.startsWith('http')) {
            imageUrls.push(url);
            console.log('从video封面提取图片:', url);
            
            // 🆕 同时尝试从video对象提取对应的Live Photo视频
            let videoUrl = null;
            if (videoObj.play_addr && videoObj.play_addr.url_list && videoObj.play_addr.url_list.length > 0) {
              videoUrl = videoObj.play_addr.url_list[0];
              console.log('🎬 从video.play_addr找到对应视频:', videoUrl);
            } else if (videoObj.bit_rate && videoObj.bit_rate.length > 0) {
              // 选择最高质量的视频
              const bestQuality = videoObj.bit_rate.reduce((prev, current) => 
                (prev.bit_rate > current.bit_rate) ? prev : current
              );
              if (bestQuality.play_addr && bestQuality.play_addr.url_list && bestQuality.play_addr.url_list.length > 0) {
                videoUrl = bestQuality.play_addr.url_list[0];
                console.log('🎬 从video.bit_rate找到最高质量视频:', videoUrl);
              }
            }
            
            videoUrls.push(videoUrl);
            break;
          }
        }
      }
    }

    // 方法3：从media或multimedia字段提取
    if (imageUrls.length === 0 && (awemeInfo.media || awemeInfo.multimedia)) {
      console.log('尝试从media/multimedia字段提取');
      const mediaObj = awemeInfo.media || awemeInfo.multimedia;
      
      if (mediaObj.images && mediaObj.images.length > 0) {
        mediaObj.images.forEach((img, index) => {
          if (img.url_list && img.url_list.length > 0) {
            const url = img.url_list[0];
            if (url && url.startsWith('http')) {
              imageUrls.push(url);
              console.log(`从media提取第${index + 1}张图片:`, url);
              
              // 🆕 同时查找对应的Live Photo视频
              let livePhotoVideoUrl = null;
              if (img.video && img.video.play_addr && img.video.play_addr.url_list && img.video.play_addr.url_list.length > 0) {
                livePhotoVideoUrl = img.video.play_addr.url_list[0];
                console.log(`🎬 从media images[${index}].video找到Live Photo视频:`, livePhotoVideoUrl);
              }
              videoUrls.push(livePhotoVideoUrl);
            }
          }
        });
      }
    }

    if (imageUrls.length === 0) {
      console.log('API响应中未找到图片信息');
      console.log('aweme信息结构:', JSON.stringify(awemeInfo, null, 2).substring(0, 1000));
      return null;
    }

    // 统一修复所有图片URL
    imageUrls = imageUrls.map(url => fixIncompleteImageUrl(url)).filter(url => url && url.startsWith('http'));
    
    // 过滤有效的视频URL
    const validVideoUrls = videoUrls.filter(url => url && url.startsWith('http'));
    
    console.log('API解析成功，图片数量:', imageUrls.length);
    console.log('Live Photo视频数量:', validVideoUrls.length);

    return {
      title: title,
      author: author,
      processedData: {
        data: imageUrls[0], // 主图片URL
        type: 'image/jpeg',
        isUrl: true,
        imageUrls: imageUrls, // 所有图片
        videoUrls: validVideoUrls, // Live Photo背景视频URL数组
        isImageContent: true,
        hasLivePhoto: validVideoUrls.length > 0, // 标识是否有Live Photo
        isMixedContent: validVideoUrls.length > 0 // 标识为混合内容
      },
      type: 'image',
      platform: 'douyin',
      source: '抖音',
      note: validVideoUrls.length > 0 ? 
        `已获取图文内容（API解析，共${imageUrls.length}张图片，${validVideoUrls.length}个Live Photo视频）` :
        `已获取图文内容（API解析，共${imageUrls.length}张图片）`,
      coverUrl: imageUrls[0]
    };

  } catch (error) {
    console.error('解析API响应失败:', error);
    return null;
  }
}

// 基于ID直接构建slides内容的函数
async function buildSlidesContentFromId(slidesId, originalUrl) {
  try {
    console.log('开始基于ID直接构建slides内容, ID:', slidesId);

    // 直接复用统一的API函数，避免重复代码
    const apiResult = await fetchSlidesDataFromAPI(slidesId);
    if (apiResult) {
      console.log('通过统一API成功获取数据');
      return apiResult;
    }

    // 如果统一API失败，尝试构建通用的slides URL
    console.log('统一API失败，尝试构建通用slides结果');
    
    // 简化处理：如果统一API失败，直接返回null

    return null;
  } catch (error) {
    console.error('基于ID构建slides内容失败:', error);
    return null;
  }
}

// 删除无用的generatePossibleImageUrls函数

// 解析 window._ROUTER_DATA 的函数 - 基于PHP代码的思路
function parseRouterData(htmlContent, videoId, originalUrl) {
  try {
    console.log('开始解析_ROUTER_DATA，页面长度:', htmlContent.length);
    
    // 使用正则表达式匹配 window._ROUTER_DATA = {...}
    const pattern = /window\._ROUTER_DATA\s*=\s*(.*?)<\/script>/s;
    const matches = htmlContent.match(pattern);
    
    if (!matches || !matches[1]) {
      console.log('未找到_ROUTER_DATA匹配');
      return null;
    }
    
    console.log('找到_ROUTER_DATA匹配，长度:', matches[1].length);
    
    let routerData;
    try {
      routerData = JSON.parse(matches[1].trim());
    } catch (parseError) {
      console.error('_ROUTER_DATA JSON解析失败:', parseError.message);
      console.log('原始数据前500字符:', matches[1].substring(0, 500));
      return null;
    }
    
    console.log('_ROUTER_DATA解析成功');
    console.log('根级别键:', Object.keys(routerData));
    
    // 查找loaderData
    if (!routerData.loaderData) {
      console.log('未找到loaderData');
      return null;
    }
    
    console.log('找到loaderData，键:', Object.keys(routerData.loaderData));
    
    // 查找视频/图文数据
    let awemeDetail = null;
    
    // 尝试多种可能的路径
    const possiblePaths = [
      routerData.loaderData['video/:id'],
      routerData.loaderData['note/:id'],
      routerData.loaderData['slides/:id'],
      routerData.loaderData[`video/${videoId}`],
      routerData.loaderData[`note/${videoId}`],
      routerData.loaderData[`slides/${videoId}`]
    ];
    
    for (const pathData of possiblePaths) {
      if (pathData && pathData.videoDetail) {
        awemeDetail = pathData.videoDetail;
        console.log('从videoDetail找到数据');
        break;
      }
      if (pathData && pathData.awemeDetail) {
        awemeDetail = pathData.awemeDetail;
        console.log('从awemeDetail找到数据');
        break;
      }
      if (pathData && pathData.data) {
        awemeDetail = pathData.data;
        console.log('从data找到数据');
        break;
      }
    }
    
    if (!awemeDetail) {
      console.log('未找到aweme详细数据');
      console.log('loaderData结构:', JSON.stringify(routerData.loaderData, null, 2).substring(0, 1000));
      return null;
    }
    
    console.log('找到aweme详细数据，键:', Object.keys(awemeDetail));
    
    // 提取基本信息
    const title = awemeDetail.desc || awemeDetail.title || '抖音内容';
    const author = awemeDetail.author?.nickname || awemeDetail.author?.uniqueId || '抖音用户';
    
    console.log('提取信息 - 标题:', title, '作者:', author);
    
    // 提取图片信息和Live Photo视频
    let imageUrls = [];
    let videoUrls = []; // 🆕 新增：Live Photo背景视频URL数组
    
    // 方法1：从images字段提取图片和对应的Live Photo视频
    if (awemeDetail.images && awemeDetail.images.length > 0) {
      console.log('找到images数组，长度:', awemeDetail.images.length);
      
      awemeDetail.images.forEach((img, index) => {
        // 提取静态图片URL
        if (img.urlList && img.urlList.length > 0) {
          const url = img.urlList[0];
          if (url && url.startsWith('http')) {
            imageUrls.push(url);
            console.log(`提取第${index + 1}张图片:`, url);
          }
        }
        
        // 🆕 新增：提取Live Photo背景视频URL
        let livePhotoVideoUrl = null;
        
        // 检查img对象的video字段
        if (img.video && img.video.playAddr && img.video.playAddr.urlList && img.video.playAddr.urlList.length > 0) {
          livePhotoVideoUrl = img.video.playAddr.urlList[0];
          console.log(`🎬 从_ROUTER_DATA images[${index}].video.playAddr找到Live Photo视频:`, livePhotoVideoUrl);
        } else if (img.livePhoto && img.livePhoto.urlList && img.livePhoto.urlList.length > 0) {
          livePhotoVideoUrl = img.livePhoto.urlList[0];
          console.log(`🎬 从_ROUTER_DATA images[${index}].livePhoto找到Live Photo视频:`, livePhotoVideoUrl);
        } else if (img.playUrl) {
          livePhotoVideoUrl = img.playUrl;
          console.log(`🎬 从_ROUTER_DATA images[${index}].playUrl找到Live Photo视频:`, livePhotoVideoUrl);
        }
        
        // 如果还没找到，尝试从img对象的其他字段查找包含douyinvod.com的URL
        if (!livePhotoVideoUrl) {
          const imgStr = JSON.stringify(img);
          const videoUrlMatch = imgStr.match(/https?:\/\/[^"]*douyinvod\.com[^"]*\.mp4[^"]*/);
          if (videoUrlMatch) {
            livePhotoVideoUrl = videoUrlMatch[0];
            console.log(`🎬 通过正则匹配在_ROUTER_DATA images[${index}]中找到Live Photo视频:`, livePhotoVideoUrl);
          }
        }
        
        videoUrls.push(livePhotoVideoUrl);
        if (livePhotoVideoUrl) {
          console.log(`✅ 第${index + 1}张图片对应的Live Photo视频已添加`);
        } else {
          console.log(`⚠️  第${index + 1}张图片没有找到对应的Live Photo视频`);
        }
      });
    }
    
    // 方法2：从video.cover提取（备用）
    if (imageUrls.length === 0 && awemeDetail.video?.cover?.urlList) {
      const coverUrl = awemeDetail.video.cover.urlList[0];
      if (coverUrl && coverUrl.startsWith('http')) {
        imageUrls.push(coverUrl);
        console.log('从video封面提取图片:', coverUrl);
        
        // 🆕 同时尝试从video对象提取对应的视频
        let videoUrl = null;
        if (awemeDetail.video.playAddr && awemeDetail.video.playAddr.urlList && awemeDetail.video.playAddr.urlList.length > 0) {
          videoUrl = awemeDetail.video.playAddr.urlList[0];
          console.log('🎬 从_ROUTER_DATA video.playAddr找到对应视频:', videoUrl);
        }
        videoUrls.push(videoUrl);
      }
    }
    
    if (imageUrls.length === 0) {
      console.log('未找到图片数据');
      return null;
    }
    
    // 统一修复所有图片URL
    imageUrls = imageUrls.map(url => fixIncompleteImageUrl(url)).filter(url => url && url.startsWith('http'));
    
    // 过滤有效的视频URL
    const validVideoUrls = videoUrls.filter(url => url && url.startsWith('http'));
    
    console.log('_ROUTER_DATA解析成功，图片数量:', imageUrls.length);
    console.log('Live Photo视频数量:', validVideoUrls.length);
    
    return {
      title: title,
      author: author,
      processedData: {
        data: imageUrls[0], // 主图片URL
        type: 'image/jpeg',
        isUrl: true,
        imageUrls: imageUrls, // 所有图片
        videoUrls: validVideoUrls, // 🆕 新增：Live Photo背景视频URL数组
        isImageContent: true,
        hasLivePhoto: validVideoUrls.length > 0, // 🆕 标识是否有Live Photo
        isMixedContent: validVideoUrls.length > 0 // 🆕 标识为混合内容
      },
      type: 'image',
      platform: 'douyin',
      source: '抖音',
      note: validVideoUrls.length > 0 ? 
        `已获取图文内容（_ROUTER_DATA解析，共${imageUrls.length}张图片，${validVideoUrls.length}个Live Photo视频）` :
        `已获取图文内容（_ROUTER_DATA解析，共${imageUrls.length}张图片）`,
      coverUrl: imageUrls[0],
      originalUrl: originalUrl
    };
    
  } catch (error) {
    console.error('解析_ROUTER_DATA失败:', error);
    return null;
  }
}

// 处理动态加载的slides页面
async function handleDynamicSlidesPage(originalUrl, realUrl) {
  try {
    console.log('开始处理动态slides页面');
    
    // 提取slides ID
    const slidesIdMatch = originalUrl.match(/\/slides\/(\d+)/);
    if (!slidesIdMatch) {
      console.log('无法从slides URL提取ID');
      return null;
    }
    
    const slidesId = slidesIdMatch[1];
    console.log('提取到slides ID:', slidesId);
    
    // 策略1：转换为video链接请求（基于PHP代码思路）
    console.log('策略1：尝试转换为video链接');
    const videoUrl = `https://www.iesdouyin.com/share/video/${slidesId}`;
    
    try {
      const videoContent = await getPageContentWithMultipleAttempts(videoUrl);
      if (videoContent && videoContent.includes('_ROUTER_DATA')) {
        console.log('video链接成功获取到_ROUTER_DATA');
        const routerResult = parseRouterData(videoContent, slidesId, originalUrl);
        if (routerResult) {
          return routerResult;
        }
      } else {
        console.log('video链接未包含_ROUTER_DATA');
      }
    } catch (videoError) {
      console.log('video链接请求失败:', videoError.message);
    }
    
    // 策略2：转换为note链接请求
    console.log('策略2：尝试转换为note链接');
    const noteUrl = `https://www.iesdouyin.com/share/note/${slidesId}`;
    
    try {
      const noteContent = await getPageContentWithMultipleAttempts(noteUrl);
      if (noteContent && (noteContent.includes('_ROUTER_DATA') || noteContent.includes('aweme_type'))) {
        console.log('note链接成功获取到数据');
        
        // 先尝试_ROUTER_DATA解析
        if (noteContent.includes('_ROUTER_DATA')) {
          const routerResult = parseRouterData(noteContent, slidesId, originalUrl);
          if (routerResult) {
            return routerResult;
          }
        }
        
        // 再尝试标准解析
        const noteResult = await parseStandardContent(noteContent, originalUrl);
        if (noteResult) {
          return noteResult;
        }
      } else {
        console.log('note链接未包含有效数据');
      }
    } catch (noteError) {
      console.log('note链接请求失败:', noteError.message);
    }
    
    // 策略3：使用移动端UA请求原始链接
    console.log('策略3：尝试移动端UA请求');
    try {
      const mobileContent = await getPageContentWithMobileUA(realUrl);
      if (mobileContent && (mobileContent.includes('_ROUTER_DATA') || mobileContent.includes('aweme_type'))) {
        console.log('移动端UA成功获取到数据');
        
        if (mobileContent.includes('_ROUTER_DATA')) {
          const routerResult = parseRouterData(mobileContent, slidesId, originalUrl);
          if (routerResult) {
            return routerResult;
          }
        }
        
        const mobileResult = await parseStandardContent(mobileContent, originalUrl);
        if (mobileResult) {
          return mobileResult;
        }
      }
    } catch (mobileError) {
      console.log('移动端UA请求失败:', mobileError.message);
    }
    
    // 策略4：API接口请求
    console.log('策略4：尝试API接口');
    const apiResult = await fetchSlidesDataFromAPI(slidesId);
    if (apiResult) {
      console.log('API接口成功获取数据');
      return apiResult;
    }
    
    console.log('所有策略都失败，返回null');
    return null;
    
  } catch (error) {
    console.error('处理动态slides页面失败:', error);
    return null;
  }
}

// 多次尝试获取页面内容
// 删除重复的页面获取函数，统一使用getPageContent

// 解析标准内容（用于note链接等）
async function parseStandardContent(content, originalUrl) {
  try {
    console.log('开始解析标准内容');
    
    // 检查内容类型
    const contentType = analyzeContentType(content);
    console.log('标准内容类型:', contentType);
    
    if (contentType === 'image') {
      const imageInfo = await extractImageInfo(content, originalUrl);
      return {
        title: imageInfo.title,
        author: imageInfo.author,
        processedData: {
          data: imageInfo.videoUrl,
          type: 'image/jpeg',
          isUrl: true,
          imageUrls: imageInfo.imageUrls,
          isImageContent: true
        },
        type: 'image',
        platform: 'douyin',
        source: '抖音',
        note: `已获取图文内容（标准解析，共${imageInfo.imageUrls.length}张图片）`,
        originalUrl: originalUrl
      };
    } else {
      const videoInfo = await extractVideoInfo(content);
      const processedUrl = await getNoWatermarkVideo(videoInfo.videoUrl);
      
      return {
        title: videoInfo.title,
        author: videoInfo.author,
        processedData: {
          data: processedUrl,
          type: 'video/mp4',
          isUrl: true,
          isDirectUrl: !processedUrl.includes('aweme.snssdk.com')
        },
        type: 'video',
        platform: 'douyin',
        source: '抖音',
        note: '已获取内容（标准解析）',
        originalUrl: originalUrl
      };
    }
  } catch (error) {
    console.error('解析标准内容失败:', error);
    return null;
  }
}

// 删除重复的fetchSlidesDataFromAPI函数（已在上方统一定义）

// 清理链接
function cleanLink(link) {
  // 移除多余的空格和换行符
  link = link.trim().replace(/\s+/g, ' ');
  
  // 提取URL
  const urlMatch = link.match(/(https?:\/\/[^\s]+)/);
  if (urlMatch) {
    return urlMatch[1];
  }
  
  return link;
}

// 从分享链接直接提取video_id
function extractVideoIdFromShareUrl(shareUrl) {
  try {
    // 方法1：从URL路径中提取
    const pathMatch = shareUrl.match(/\/video\/(\d+)/);
    if (pathMatch) {
      return pathMatch[1];
    }

    // 方法2：从短链接中提取（支持更多格式）
    const shortMatch = shareUrl.match(/v\.douyin\.com\/([A-Za-z0-9\-_]+)/);
    if (shortMatch) {
      console.log('检测到短链接:', shortMatch[1], '需要进一步解析');
      return null; // 短链接需要通过其他方式获取
    }

    // 方法3：从note链接中提取
    const noteMatch = shareUrl.match(/\/note\/(\d+)/);
    if (noteMatch) {
      console.log('检测到图文note:', noteMatch[1]);
      return noteMatch[1];
    }

    return null;
  } catch (error) {
    console.error('提取video_id失败:', error);
    return null;
  }
}

// 构建直接的无水印URL（异步版本，会验证URL有效性）
async function buildDirectNoWatermarkUrl(videoId) {
  return await tryMultipleVideoUrls(videoId);
}

// 解析API链接获取真实视频地址
async function resolveRealVideoUrl(apiUrl) {
  try {
    console.log('开始解析真实视频地址:', apiUrl);

    const response = await uniCloud.httpclient.request(apiUrl, {
      method: 'GET',
      timeout: 15000,
      followRedirect: false, // 不自动跟随重定向，我们要手动处理
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'Referer': 'https://www.douyin.com/',
        'Accept': '*/*'
      }
    });

    console.log('API响应状态:', response.status);
    console.log('API响应头:', response.headers);

    // 检查是否有重定向
    if (response.status === 302 || response.status === 301) {
      const location = response.headers.location || response.headers.Location;
      if (location) {
        console.log('找到重定向地址:', location);
        return location;
      }
    }

    // 如果是200状态，检查响应内容
    if (response.status === 200) {
      const contentType = response.headers['content-type'] || response.headers['Content-Type'] || '';

      // 如果是视频文件，直接返回原URL
      if (contentType.includes('video/') || contentType.includes('application/octet-stream')) {
        console.log('API直接返回视频内容');
        return apiUrl;
      }

      // 如果是JSON响应，尝试解析
      if (contentType.includes('application/json')) {
        try {
          const jsonData = JSON.parse(response.data);
          if (jsonData.video_url || jsonData.play_url) {
            const realUrl = jsonData.video_url || jsonData.play_url;
            console.log('从JSON响应中找到视频地址:', realUrl);
            return realUrl;
          }
        } catch (parseError) {
          console.log('解析JSON响应失败:', parseError.message);
        }
      }
    }

    console.log('无法解析真实地址，返回原API地址');
    return apiUrl;

  } catch (error) {
    console.log('解析真实视频地址失败:', error.message);
    return apiUrl; // 失败时返回原API地址
  }
}

// 验证视频URL是否有效（支持API地址和直接地址）
async function validateVideoUrl(videoUrl) {
  try {
    // 如果是API地址，先解析真实地址
    let urlToTest = videoUrl;
    if (videoUrl.includes('aweme.snssdk.com') || videoUrl.includes('aweme-')) {
      console.log('检测到API地址，先解析真实地址');
      urlToTest = await resolveRealVideoUrl(videoUrl);
      console.log('解析后的地址:', urlToTest);
    }

    const response = await uniCloud.httpclient.request(urlToTest, {
      method: 'HEAD',
      timeout: 12000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'Referer': 'https://www.douyin.com/',
        'Accept': '*/*',
        'Accept-Encoding': 'identity'
      }
    });

    console.log('URL验证结果:', response.status);
    console.log('响应头:', response.headers);

    // 检查HTTP状态码
    if (response.status !== 200) {
      console.log('HTTP状态码不是200:', response.status);
      return { isValid: false, realUrl: urlToTest };
    }

    // 检查Content-Type，确保是视频文件
    const contentType = response.headers['content-type'] || response.headers['Content-Type'] || '';
    console.log('Content-Type:', contentType);

    // 如果返回的是JSON或HTML，说明不是视频文件
    if (contentType.includes('application/json') || contentType.includes('text/html') || contentType.includes('text/plain')) {
      console.log('Content-Type表明这不是视频文件:', contentType);
      return { isValid: false, realUrl: urlToTest };
    }

    // 检查Content-Length，如果太小可能不是有效的视频
    const contentLength = response.headers['content-length'] || response.headers['Content-Length'];
    if (contentLength && parseInt(contentLength) < 1024) {
      console.log('Content-Length太小，可能不是有效视频:', contentLength);
      return { isValid: false, realUrl: urlToTest };
    }

    return { isValid: true, realUrl: urlToTest };
  } catch (error) {
    console.log('URL验证失败:', error.message);
    return { isValid: false, realUrl: videoUrl };
  }
}

// 尝试多个视频URL格式（支持多清晰度）
async function tryMultipleVideoUrls(videoId) {
  // 清晰度优先级（从高到低）
  const qualities = ['1080p', '720p', '540p', '480p', '360p'];

  // 域名选项
  const domains = [
    'aweme.snssdk.com',
    'aweme-hl.snssdk.com',
    'aweme-eagle.snssdk.com'
  ];

  const urlVariants = [];

  // 生成所有可能的URL组合
  for (const quality of qualities) {
    for (const domain of domains) {
      // 标准格式
      urlVariants.push(`https://${domain}/aweme/v1/play/?video_id=${videoId}&ratio=${quality}&line=0&watermark=0&media_type=4&vr_type=0&improve_bitrate=0&is_play_url=1&source=PackSourceEnum_PUBLISH`);

      // 简化格式
      urlVariants.push(`https://${domain}/aweme/v1/play/?video_id=${videoId}&watermark=0&ratio=${quality}`);
    }
  }

  console.log(`生成了${urlVariants.length}个候选URL，开始测试...`);

  for (let i = 0; i < urlVariants.length; i++) {
    const url = urlVariants[i];
    console.log(`测试URL ${i + 1}/${urlVariants.length}:`, url);

    const result = await validateVideoUrl(url);
    if (result.isValid) {
      console.log('找到有效URL:', url);
      console.log('解析后的真实地址:', result.realUrl);

      // 返回真实的视频地址，不是API地址
      return result.realUrl;
    }
  }

  console.log('所有URL都无效，返回第一个API地址');
  return urlVariants[0];
}







async function parseDouyinVideo(shareUrl) {
  console.log('开始解析抖音链接:', shareUrl);

  try {
    // 预检查：根据URL特征判断内容类型
    const urlHasNote = shareUrl.includes('/note/') || shareUrl.includes('note');
    const urlPattern = shareUrl.match(/v\.douyin\.com\/([A-Za-z0-9\-_]+)/);
    const shortCode = urlPattern ? urlPattern[1] : '';
    console.log('URL预检查:');
    console.log('- 包含note:', urlHasNote);
    console.log('- 短代码:', shortCode);
    console.log('- 短代码包含特殊字符:', shortCode.includes('-') || shortCode.includes('_'));

    // 第一步：尝试直接从分享链接提取video_id（仅对视频）
    if (!urlHasNote) {
      const directVideoId = extractVideoIdFromShareUrl(shareUrl);
      if (directVideoId) {
        console.log('从分享链接直接提取到video_id:', directVideoId);
        const directUrl = await buildDirectNoWatermarkUrl(directVideoId);

        // buildDirectNoWatermarkUrl已经验证了URL有效性并返回真实地址
        const isDirectVideo = !directUrl.includes('aweme.snssdk.com') && !directUrl.includes('aweme-');

        return {
          title: '抖音视频',
          author: '抖音用户',
          processedData: {
            data: directUrl,
            type: 'video/mp4',
            isUrl: true,
            isDirectUrl: isDirectVideo
          },
          type: 'video',
          platform: 'douyin',
          source: '抖音',
          note: isDirectVideo ? '已获取无水印直链' : '已获取API链接',
          originalUrl: shareUrl // 保留用户输入的原始分享链接
        };
      }
    }

    // 第二步：获取真实URL
    let realUrl;
    try {
      realUrl = await getRealUrl(shareUrl);
      console.log('真实URL:', realUrl);
    } catch (urlError) {
      console.error('获取真实URL失败，使用原始URL:', urlError.message);
      realUrl = shareUrl;
    }

    // 第三步：获取页面内容，如果失败则使用备用方案
    let pageContent;
    try {
      pageContent = await getPageContent(realUrl);
      console.log('页面内容获取成功');
    } catch (pageError) {
      console.error('获取页面内容失败:', pageError.message);
      
      // 如果是网络超时或连接失败，尝试直接解析URL中的ID
      if (pageError.message.includes('timeout') || pageError.message.includes('ECONNRESET') || pageError.message.includes('ETIMEDOUT')) {
        console.log('检测到网络问题，尝试直接ID解析方案');
        
        const directVideoId = extractVideoIdFromShareUrl(shareUrl);
        if (directVideoId) {
          console.log('从URL直接提取到ID:', directVideoId);
          const directUrl = await buildDirectNoWatermarkUrl(directVideoId);
          
          return {
            title: '抖音内容',
            author: '抖音用户',
            processedData: {
              data: directUrl,
              type: 'video/mp4',
              isUrl: true,
              isDirectUrl: !directUrl.includes('aweme.snssdk.com')
            },
            type: 'video',
            platform: 'douyin',
            source: '抖音',
            note: '网络不稳定，已获取备用链接',
            originalUrl: shareUrl
          };
        }
      }
      
      throw pageError; // 如果无法处理，继续抛出原错误
    }

    // 调试：输出页面内容的关键部分
    // 页面分析开始
    console.log('页面长度:', pageContent.length);
    console.log('URL类型检查:');
    console.log('- 原始URL:', shareUrl);
    console.log('- 真实URL:', realUrl);
    console.log('- 原始URL是否slides链接:', shareUrl.includes('/share/slides/'));
    console.log('- 真实URL是否slides链接:', realUrl.includes('/share/slides/'));
    
    // 判断是否为slides类型（需要检查真实URL）
    const isSlidesContent = shareUrl.includes('/share/slides/') || realUrl.includes('/share/slides/');
    console.log('- 综合判断是否slides:', isSlidesContent);
    
    console.log('页面内容关键标识:');
    console.log('- 包含_ROUTER_DATA:', pageContent.includes('_ROUTER_DATA'));
    console.log('- 包含aweme_type:', pageContent.includes('aweme_type'));
    console.log('- 包含images:', pageContent.includes('"images"'));
    console.log('- 包含video:', pageContent.includes('"video"'));
    console.log('- 包含html标签:', pageContent.includes('<html>'));
    console.log('- 包含body标签:', pageContent.includes('<body>'));
    console.log('- 包含script标签:', pageContent.includes('<script>'));
    
    // 检查页面是否基本为空
    if (pageContent.length < 1000) {
      console.log('⚠️  页面内容过短，可能获取失败');
      console.log('完整页面内容:');
      console.log(pageContent);
      // 页面内容结束
    } else if (pageContent.length < 10000 && !pageContent.includes('aweme')) {
      console.log('⚠️  页面可能是空壳页面');
      console.log('页面开头500字符:');
      console.log(pageContent.substring(0, 500));
      console.log('页面结尾500字符:');
      console.log(pageContent.substring(pageContent.length - 500));
    }
    
    // 输出页面结构特征
    if (pageContent.length < 5000) {
      // 完整页面内容
      console.log(pageContent);
    } else {
      // 页面开头1000字符
      console.log(pageContent.substring(0, 1000));
      console.log('=== 页面结尾1000字符 ===');
      console.log(pageContent.substring(pageContent.length - 1000));
    }
    console.log('=== 页面分析结束 ===');
    
    // 先检查最基础的：页面是否包含有效数据
    if (!pageContent.includes('aweme') && !pageContent.includes('douyin')) {
      console.log('❌ 页面不包含抖音相关内容，可能是错误页面');
      throw new Error('页面内容异常，不包含抖音数据');
    }
    
    // 🔍 **核心发现：检测SSR页面**
    const isSSRPage = pageContent.includes('slides_ssr') || 
                     pageContent.includes('SSRDataScript') ||
                     (pageContent.length > 20000 && pageContent.includes('chunksMap.js'));
    
    console.log('🔍 SSR页面检测:', isSSRPage);
    
    if (isSSRPage && isSlidesContent) {
      console.log('🚀 检测到slides SSR页面，使用专门解析策略');
      return await parseSlidesSSRPage(shareUrl, realUrl, pageContent);
    }
    
    // 分析内容类型
    const contentType = analyzeContentType(pageContent);
    console.log('内容类型分析结果:', contentType);

    let videoInfo;
    let isVideoContent;

    if (contentType === 'image') {
      console.log('确定为图文内容，使用图文解析...');
      videoInfo = await extractImageInfo(pageContent, shareUrl);
      isVideoContent = false;
    } else {
      // 默认按视频处理（包括 'video' 和 'unknown' 情况）
      console.log('按视频内容处理...');
      try {
        videoInfo = await extractVideoInfo(pageContent);
        isVideoContent = true;
      } catch (extractError) {
        console.error('从HTML提取视频信息失败:', extractError);

        // 如果HTML提取失败，尝试直接构建URL作为备用方案
        const directVideoId = extractVideoIdFromShareUrl(shareUrl);
        if (directVideoId) {
          console.log('HTML提取失败，尝试直接构建URL，video_id:', directVideoId);
          const directUrl = await buildDirectNoWatermarkUrl(directVideoId);

          videoInfo = {
            title: '抖音视频',
            author: '抖音用户',
            videoUrl: directUrl,
            duration: 0
          };
          isVideoContent = true;
        } else {
          throw extractError; // 如果都失败了，抛出原始错误
        }
      }
    }

    // 第五步：根据内容类型处理
    let finalVideoUrl;
    if (isVideoContent) {
      // 对提取的视频URL进行去水印处理
      let processedUrl = await getNoWatermarkVideo(videoInfo.videoUrl);
      console.log('去水印处理后的URL:', processedUrl);

      // 如果是API地址，进行二次解析获取真实地址
      if (processedUrl.includes('aweme.snssdk.com') || processedUrl.includes('aweme-')) {
        console.log('检测到API地址，进行二次解析...');
        finalVideoUrl = await resolveRealVideoUrl(processedUrl);
        // 真实地址已在resolveRealVideoUrl中打印，无需重复
      } else {
        finalVideoUrl = processedUrl;
      }
    } else {
      finalVideoUrl = videoInfo.videoUrl; // 图文内容直接使用图片URL
    }

    // 第六步：根据内容类型处理数据
    let processedData;
    let note;
    let resultType;

    if (isVideoContent) {
      // 视频内容处理
      const durationInSeconds = Math.round(videoInfo.duration / 1000);
      const isLongVideo = durationInSeconds > 120;
      const isDirectVideo = !finalVideoUrl.includes('aweme.snssdk.com') && !finalVideoUrl.includes('aweme-');

      processedData = {
        data: finalVideoUrl,
        type: 'video/mp4',
        isUrl: true,
        duration: durationInSeconds,
        isLongVideo: isLongVideo,
        isDirectUrl: isDirectVideo
      };
      resultType = 'video';

      if (isLongVideo) {
        if (isDirectVideo) {
          note = `已获取无水印直链（时长${Math.floor(durationInSeconds/60)}分${durationInSeconds%60}秒，文件较大）`;
        } else {
          note = `已获取API链接（时长${Math.floor(durationInSeconds/60)}分${durationInSeconds%60}秒，文件较大）`;
        }
      } else {
        if (isDirectVideo) {
          note = '已获取无水印直链';
        } else {
          note = '已获取API链接';
        }
      }
    } else {
      // 图文内容处理
      processedData = {
        data: videoInfo.videoUrl, // 主图片URL
        type: 'image/jpeg',
        isUrl: true,
        imageUrls: videoInfo.imageUrls, // 所有图片
        isImageContent: true
      };
      resultType = 'image';
      note = `已获取图文内容（共${videoInfo.imageUrls.length}张图片）`;
    }

    return {
      title: videoInfo.title,
      author: videoInfo.author,
      processedData: processedData,
      type: resultType, // 'video' 或 'image'
      platform: 'douyin',
      source: '抖音',
      note: note,
      coverUrl: videoInfo.coverUrl, // 添加封面URL
      originalUrl: shareUrl // 保留用户输入的原始分享链接
    };
    
  } catch (error) {
    console.error('抖音解析失败:', error);
    throw error;
  }
}

async function getRealUrl(shortUrl) {
  const maxRetries = 2;
  const timeout = 8000;
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      console.log(`获取真实URL (尝试 ${attempt + 1}/${maxRetries}):`, shortUrl);

      const response = await uniCloud.httpclient.request(shortUrl, {
        method: 'GET',
        followRedirect: false,
        timeout: timeout,
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
      });

      console.log('响应状态码:', response.status);
      console.log('响应头:', response.headers);

      const location = response.headers.location || response.headers.Location;
      const realUrl = location || shortUrl;

      console.log('真实URL:', realUrl);
      return realUrl;

    } catch (error) {
      console.error(`第${attempt + 1}次获取真实URL失败:`, error.message);
      
      // 如果是最后一次尝试，返回原始URL
      if (attempt === maxRetries - 1) {
        console.log('所有尝试都失败，使用原始URL:', shortUrl);
        return shortUrl;
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

async function getPageContent(url) {
  const maxRetries = 3;
  const timeouts = [10000, 20000, 30000]; // 递增超时时间
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      console.log(`获取页面内容 (尝试 ${attempt + 1}/${maxRetries}):`, url);
      console.log(`使用超时时间: ${timeouts[attempt]}ms`);

      const response = await uniCloud.httpclient.request(url, {
        method: 'GET',
        timeout: timeouts[attempt],
        dataType: 'text',
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Referer': 'https://www.douyin.com/',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive'
        }
      });

      console.log('页面获取成功，状态:', response.status);
      console.log('页面内容长度:', response.data ? response.data.length : 0);

      // 检查响应状态
      if (response.status !== 200) {
        throw new Error(`HTTP状态码错误: ${response.status}`);
      }

      // 确保返回字符串
      let htmlContent = response.data;
      if (typeof htmlContent !== 'string') {
        if (htmlContent && htmlContent.toString) {
          htmlContent = htmlContent.toString();
        } else {
          throw new Error('页面内容格式错误');
        }
      }

      // 检查内容是否有效
      if (!htmlContent || htmlContent.length < 100) {
        throw new Error('页面内容过短，可能加载失败');
      }

      // 检查是否包含抖音相关内容
      if (!htmlContent.includes('douyin') && !htmlContent.includes('aweme')) {
        console.log('警告: 页面内容不包含抖音相关标识，可能是错误页面');
      }

      return htmlContent;

    } catch (error) {
      console.error(`第${attempt + 1}次尝试失败:`, error.message);
      
      // 如果是最后一次尝试，直接抛出错误
      if (attempt === maxRetries - 1) {
        console.error('所有尝试都失败，抛出最终错误');
        throw new Error('无法获取页面内容: ' + error.message);
      }
      
      // 等待一段时间再重试
      const waitTime = (attempt + 1) * 2000; // 2秒, 4秒, 6秒
      console.log(`等待 ${waitTime}ms 后重试...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
}

async function extractVideoInfo(html) {
  try {
    console.log('开始提取视频信息...');
    console.log('HTML内容类型:', typeof html);
    console.log('HTML内容长度:', html ? html.length : 0);

    // 确保html是字符串
    if (typeof html !== 'string') {
      throw new Error('HTML内容不是字符串格式');
    }

    if (!html || html.length < 100) {
      throw new Error('HTML内容太短，可能获取失败');
    }

    console.log('开始提取视频信息...');
    console.log('HTML内容片段（前1000字符）:', html.substring(0, 1000));

    // 提取标题 - 使用JSON解析而不是正则表达式
    let title = '未知标题';
    
    // 方法1：查找并解析包含desc字段的JSON对象
    console.log('开始查找JSON数据中的desc字段...');
    
    try {
      // 查找所有可能的JSON片段
      const jsonPatterns = [
        // 主要的JSON数据结构
        /window\._ROUTER_DATA\s*=\s*({.+?})\s*<\/script>/s,
        /window\.__INITIAL_STATE__\s*=\s*({.+?})\s*;/s,
        /__NUXT__\s*=\s*({.+?})\s*;/s,
        
        // 局部包含aweme或desc的JSON
        /"aweme_detail":\s*({.+?"desc".+?})/s,
        /"aweme":\s*({.+?"desc".+?})/s,
        /"item":\s*({.+?"desc".+?})/s,
        
        // 直接查找包含desc的JSON对象
        /{[^{}]*"desc":\s*"[^"]+"\s*[^{}]*}/g
      ];
      
      let foundTitle = false;
      
      // 尝试从大的JSON结构中提取
      for (let i = 0; i < jsonPatterns.length - 1; i++) {
        const pattern = jsonPatterns[i];
        const match = html.match(pattern);
        if (match && match[1]) {
          try {
            console.log(`extractVideoInfo: 尝试JSON模式${i + 1}，找到JSON片段，长度:`, match[1].length);
            console.log(`extractVideoInfo: JSON片段前500字符:`, match[1].substring(0, 500));
            const jsonData = JSON.parse(match[1]);
            
            // 视频解析不再打印详细JSON结构
            
            // 递归查找desc字段
            const desc = findFieldInObject(jsonData, 'desc', ['title', 'content', 'text', 'description', 'aweme_desc']);
            if (desc && desc.trim() && desc.length > 2) {
              title = desc.trim();
              foundTitle = true;
              console.log('extractVideoInfo: 从JSON对象中提取到标题:', title);
              console.log('extractVideoInfo: 标题长度:', title.length);
              break;
            } else {
              console.log('extractVideoInfo: 未找到有效的desc字段或desc为空');
            }
          } catch (jsonError) {
            console.log(`JSON模式${i + 1}解析失败:`, jsonError.message);
            continue;
          }
        }
      }
      
      // 如果大JSON解析失败，尝试查找包含desc的小JSON片段
      if (!foundTitle) {
        console.log('大JSON解析失败，尝试查找包含desc的JSON片段...');
        const lastPattern = jsonPatterns[jsonPatterns.length - 1];
        const descJsonMatches = html.match(lastPattern);
        if (descJsonMatches && descJsonMatches.length > 0) {
          console.log('找到', descJsonMatches.length, '个包含desc的JSON片段');
          for (let i = 0; i < descJsonMatches.length; i++) {
            const jsonStr = descJsonMatches[i];
            try {
              console.log('尝试解析JSON片段', i + 1, ':', jsonStr.substring(0, 100) + '...');
              const obj = JSON.parse(jsonStr);
              if (obj.desc && obj.desc.trim() && obj.desc.length > 2) {
                title = obj.desc.trim();
                foundTitle = true;
                console.log('从小JSON片段中提取到标题:', title);
                break;
              }
            } catch (e) {
              console.log('JSON片段', i + 1, '解析失败:', e.message);
              continue;
            }
          }
        }
      }
      
    } catch (error) {
      console.log('JSON解析过程出错:', error.message);
    }
    
    // 方法2：如果JSON解析失败，使用title标签作为备选
    if (!title || title === '未知标题' || title.length < 3) {
      console.log('JSON解析失败，尝试title标签...');
      const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
      if (titleMatch) {
        title = titleMatch[1].replace(/\s*-\s*抖音$/, '').trim();
        console.log('从title标签提取到标题:', title);
      }
    }
    
    console.log('最终提取到标题:', title);

    // 提取作者 - 使用JSON解析方式，移除不稳定的正则表达式
    let author = '未知作者';
    console.log('extractVideoInfo: 开始使用JSON解析提取作者...');
    try {
      // 查找所有可能的JSON片段中的nickname字段
      const jsonPatterns = [
        /window\._ROUTER_DATA\s*=\s*({.+?})\s*<\/script>/s,
        /window\.__INITIAL_STATE__\s*=\s*({.+?})\s*;/s,
        /__NUXT__\s*=\s*({.+?})\s*;/s
      ];
      
      let foundAuthor = false;
      
      for (let i = 0; i < jsonPatterns.length; i++) {
        const pattern = jsonPatterns[i];
        const match = html.match(pattern);
        if (match && match[1]) {
          try {
            const jsonData = JSON.parse(match[1]);
            
            // 递归查找nickname字段
            const nickname = findFieldInObject(jsonData, 'nickname', ['author', 'user', 'name']);
            if (nickname && nickname.trim()) {
              author = nickname.trim();
              foundAuthor = true;
              console.log('extractVideoInfo: 从JSON对象中提取到作者:', author);
              break;
            }
          } catch (jsonError) {
            console.log(`extractVideoInfo: 作者JSON模式${i + 1}解析失败:`, jsonError.message);
            continue;
          }
        }
      }
      
      if (!foundAuthor) {
        console.log('extractVideoInfo: JSON解析作者失败，保持默认作者');
      }
      
    } catch (error) {
      console.log('extractVideoInfo: 作者JSON过程出错:', error.message);
    }
    console.log('extractVideoInfo: 提取到作者:', author);

    // 提取视频时长 - 使用JSON解析方式
    let duration = 0;
    console.log('extractVideoInfo: 开始使用JSON解析提取视频时长...');
    try {
      const jsonPatterns = [
        /window\._ROUTER_DATA\s*=\s*({.+?})\s*<\/script>/s,
        /window\.__INITIAL_STATE__\s*=\s*({.+?})\s*;/s,
        /__NUXT__\s*=\s*({.+?})\s*;/s
      ];
      
      let foundDuration = false;
      
      for (let i = 0; i < jsonPatterns.length; i++) {
        const pattern = jsonPatterns[i];
        const match = html.match(pattern);
        if (match && match[1]) {
          try {
            const jsonData = JSON.parse(match[1]);
            
            // 递归查找duration字段
            const durationValue = findFieldInObject(jsonData, 'duration', ['time', 'length']);
            if (durationValue && !isNaN(durationValue)) {
              duration = parseInt(durationValue);
              foundDuration = true;
              console.log('extractVideoInfo: 从JSON对象中提取到视频时长:', duration, '毫秒');
              break;
            }
          } catch (jsonError) {
            console.log(`extractVideoInfo: 时长JSON模式${i + 1}解析失败:`, jsonError.message);
            continue;
          }
        }
      }
      
      if (!foundDuration) {
        console.log('extractVideoInfo: JSON解析时长失败，保持默认值0');
      }
      
    } catch (error) {
      console.log('extractVideoInfo: 时长JSON过程出错:', error.message);
    }
    console.log('提取到视频时长:', duration, '毫秒');

    // 提取视频URL - 使用多种方法（包括图文作品的视频）
    let videoUrl = null;

    // 开始提取视频URL，使用标准方法

    // 方法2：查找无水印H264视频
    if (!videoUrl) {
      const noWatermarkMatches = html.match(/"play_addr_h264"[^}]+?"url_list":\s*\[\s*"([^"]+)"/);
      if (noWatermarkMatches) {
        videoUrl = noWatermarkMatches[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
        console.log('方法2找到无水印视频URL:', videoUrl);
      }
    }

    // 方法2：查找原画质视频
    if (!videoUrl) {
      const originMatches = html.match(/"play_addr_265"[^}]+?"url_list":\s*\[\s*"([^"]+)"/);
      if (originMatches) {
        videoUrl = originMatches[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
        console.log('方法2找到原画质视频URL:', videoUrl);
      }
    }

    // 方法3：匹配普通play_addr
    if (!videoUrl) {
      const playAddrMatch = html.match(/"play_addr"[^}]+?"url_list":\s*\[\s*"([^"]+)"/);
      if (playAddrMatch) {
        videoUrl = playAddrMatch[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
        console.log('方法3找到视频URL:', videoUrl);
      }
    }

    // 方法4：专门针对图文作品的视频URL
    if (!videoUrl) {
      const imageVideoMatch = html.match(/"video"[^}]*?"play_addr"[^}]+?"url_list":\s*\[\s*"([^"]+)"/);
      if (imageVideoMatch) {
        videoUrl = imageVideoMatch[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
        console.log('方法4找到图文作品视频URL:', videoUrl);
      }
    }

    // 方法5：查找任何可能的视频URL
    if (!videoUrl) {
      const anyVideoMatch = html.match(/https?:\/\/[^"'\s]*\.mp4[^"'\s]*/);
      if (anyVideoMatch) {
        videoUrl = anyVideoMatch[0];
        console.log('方法5找到任意视频URL:', videoUrl);
      }
    }

    // 方法6：查找较小尺寸的视频
    if (!videoUrl) {
      const smallVideoMatch = html.match(/"play_addr_lowbr"[^}]+?"url_list":\s*\[\s*"([^"]+)"/);
      if (smallVideoMatch) {
        videoUrl = smallVideoMatch[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
        console.log('方法6找到小尺寸视频URL:', videoUrl);
      }
    }

    // 方法2：匹配video标签src
    if (!videoUrl) {
      const videoSrcMatch = html.match(/<video[^>]+src="([^"]+)"/i);
      if (videoSrcMatch) {
        videoUrl = videoSrcMatch[1];
        console.log('方法2找到视频URL:', videoUrl);
      }
    }

    // 方法3：匹配任何包含mp4的URL
    if (!videoUrl) {
      const mp4Match = html.match(/https?:\/\/[^"'\s]+\.mp4[^"'\s]*/);
      if (mp4Match) {
        videoUrl = mp4Match[0];
        console.log('方法3找到视频URL:', videoUrl);
      }
    }

    // 方法4：查找无水印视频URL
    if (!videoUrl) {
      const noWatermarkMatches = html.match(/"play_addr_h264"[^}]+?"url_list":\s*\[\s*"([^"]+)"/);
      if (noWatermarkMatches) {
        videoUrl = noWatermarkMatches[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
        console.log('方法4找到无水印视频URL:', videoUrl);
      }
    }

    // 方法5：查找原画质视频
    if (!videoUrl) {
      const originMatches = html.match(/"play_addr_265"[^}]+?"url_list":\s*\[\s*"([^"]+)"/);
      if (originMatches) {
        videoUrl = originMatches[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
        console.log('方法5找到原画质视频URL:', videoUrl);
      }
    }

    // 方法6：更宽泛的视频URL匹配
    if (!videoUrl) {
      const videoUrlMatches = html.match(/https?:\/\/[^"'\s]*video[^"'\s]*/g);
      if (videoUrlMatches && videoUrlMatches.length > 0) {
        videoUrl = videoUrlMatches[0];
        console.log('方法6找到视频URL:', videoUrl);
      }
    }

    if (!videoUrl) {
      // 如果都找不到，输出部分HTML内容用于调试
      console.log('HTML片段（前500字符）:', html.substring(0, 500));
      throw new Error('无法提取视频URL，可能页面结构已变化');
    }

    // 基本URL格式检查（不进行网络验证，避免过于严格）
    console.log('检查提取的视频URL格式:', videoUrl);
    if (!videoUrl.startsWith('http')) {
      console.log('URL格式无效');
      throw new Error('提取的视频URL格式无效');
    }

    console.log('最终视频URL:', videoUrl);

    // 提取封面URL
    let coverUrl = null;
    const coverMatch = html.match(/"cover"[^}]*?"url_list":\s*\[\s*"([^"]+)"/);
    if (coverMatch) {
      let url = coverMatch[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
      if (url.startsWith('http')) {
        coverUrl = url;
        console.log('提取到封面URL:', coverUrl);
      }
    }

    return {
      title,
      author,
      videoUrl,
      coverUrl, // 添加封面URL
      duration: duration // 添加时长信息
    };

  } catch (error) {
    console.error('提取视频信息失败:', error);
    throw error;
  }
}

// 提取图文信息（包括图文视频）
async function extractImageInfo(html, originalUrl = '') {
  try {
    console.log('开始提取图文信息...');
    console.log('HTML长度:', html.length);

    // 🆕 输出HTML中包含的关键内容用于调试
    console.log('🔍 调试：检查HTML中的关键字段...');
    
    // 检查是否包含video相关字段
    const hasVideo = html.includes('"video":');
    const hasPlayAddr = html.includes('"play_addr":');
    const hasAnimatedCover = html.includes('"animated_cover":');
    const hasDynamicCover = html.includes('"dynamic_cover":');
    const hasMp4Files = /\.mp4/i.test(html);
    const hasWebmFiles = /\.webm/i.test(html);
    const hasCover = html.includes('"cover":');
    const hasOriginCover = html.includes('"origin_cover":');
    
    console.log('HTML内容检查结果:');
    console.log('- 包含video字段:', hasVideo);
    console.log('- 包含play_addr字段:', hasPlayAddr);
    console.log('- 包含animated_cover字段:', hasAnimatedCover);
    console.log('- 包含dynamic_cover字段:', hasDynamicCover);
    console.log('- 包含.mp4文件:', hasMp4Files);
    console.log('- 包含.webm文件:', hasWebmFiles);
    console.log('- 包含cover字段:', hasCover);
    console.log('- 包含origin_cover字段:', hasOriginCover);
    
    // 简化调试输出
    console.log('🎬 检测HTML内容特征完成');
    
    // 🆕 搜索所有可能的URL（重点查找douyinvod.com域名）
    console.log('🔍 搜索所有可能的视频URL:');
    const urlPatterns = [
      /https?:\/\/[^"'\s]*\.mp4[^"'\s]*/g,
      /https?:\/\/[^"'\s]*\.webm[^"'\s]*/g,
      /https?:\/\/[^"'\s]*aweme[^"'\s]*play[^"'\s]*/g,
      /https?:\/\/[^"'\s]*douyinvod\.com[^"'\s]*/g  // 合并所有douyinvod域名匹配
    ];
    
    let foundLivePhotoVideos = []; // 收集找到的Live Photo视频URL
    
    urlPatterns.forEach((pattern, index) => {
      const matches = html.match(pattern);
      if (matches && matches.length > 0) {
        // 特别处理douyinvod.com域名的视频
        if (pattern.source.includes('douyinvod')) {
          matches.forEach(match => {
            const cleanUrl = match.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
            if (cleanUrl.includes('douyinvod.com') && !foundLivePhotoVideos.includes(cleanUrl)) {
              foundLivePhotoVideos.push(cleanUrl);
            }
          });
        }
      }
    });
    
    if (foundLivePhotoVideos.length > 0) {
      console.log(`🎉 通过URL模式匹配找到 ${foundLivePhotoVideos.length} 个Live Photo视频候选:`, foundLivePhotoVideos);
    }
    
    // 🆕 新增：查找aweme.snssdk.com重定向URL
    console.log('🔍 查找重定向URL (aweme.snssdk.com):');
    
    // 注意：重定向URL将从JSON解析中统一提取，避免重复处理

    // 提取标题 - 使用JSON解析而不是正则表达式
    let title = '图文内容';
    
    console.log('开始使用JSON解析提取图文标题...');
    try {
      // 查找所有可能的JSON片段
      const jsonPatterns = [
        // 主要的JSON数据结构
        /window\._ROUTER_DATA\s*=\s*({.+?})\s*<\/script>/s,
        /window\.__INITIAL_STATE__\s*=\s*({.+?})\s*;/s,
        /__NUXT__\s*=\s*({.+?})\s*;/s,
        
        // 局部包含aweme或desc的JSON
        /"aweme_detail":\s*({.+?"desc".+?})/s,
        /"aweme":\s*({.+?"desc".+?})/s,
        /"item":\s*({.+?"desc".+?})/s,
        
        // 直接查找包含desc的JSON对象
        /{[^{}]*"desc":\s*"[^"]+"\s*[^{}]*}/g
      ];
      
      let foundTitle = false;
      
      // 尝试从大的JSON结构中提取
      for (let i = 0; i < jsonPatterns.length - 1; i++) {
        const pattern = jsonPatterns[i];
        const match = html.match(pattern);
        if (match && match[1]) {
          try {
            console.log(`图文解析JSON模式${i + 1}，找到JSON片段，长度:`, match[1].length);
            const jsonData = JSON.parse(match[1]);
            
            // 打印图文解析的完整JSON结构
            console.log('图文解析JSON对象的主要键:', Object.keys(jsonData));
            console.log('图文解析JSON完整结构:', JSON.stringify(jsonData, null, 2));
            
            // 从JSON中提取重定向URL（统一处理）
            const jsonStr = JSON.stringify(jsonData);
            const redirectUrls = [];
            const redirectPatterns = [
              /https?:\/\/aweme\.snssdk\.com\/aweme\/v1\/playwm\/[^"'\s]*/g,
              /https?:\/\/[^"'\s]*snssdk\.com[^"'\s]*playwm[^"'\s]*/g,
              /https?:\/\/[^"'\s]*aweme[^"'\s]*playwm[^"'\s]*/g
            ];
            
            redirectPatterns.forEach((pattern, index) => {
              const matches = jsonStr.match(pattern);
              if (matches && matches.length > 0) {
                matches.forEach(match => {
                  const cleanUrl = match.replace(/\\\//g, '/').replace(/"/g, '').trim();
                  if (cleanUrl && !redirectUrls.includes(cleanUrl)) {
                    redirectUrls.push(cleanUrl);
                    console.log(`🔗 从JSON中发现重定向URL: ${cleanUrl}`);
                  }
                });
              }
            });
            
            if (redirectUrls.length > 0) {
              console.log(`🎯 从JSON中找到 ${redirectUrls.length} 个重定向URL`);
            }
            
            // 递归查找desc字段
            const desc = findFieldInObject(jsonData, 'desc', ['title', 'content', 'text', 'description', 'aweme_desc']);
            if (desc && desc.trim() && desc.length > 2) {
              title = desc.trim();
              foundTitle = true;
              console.log('图文解析：从JSON对象中提取到标题:', title);
              console.log('图文解析：标题长度:', title.length);
              break;
            } else {
              console.log('图文解析：未找到有效的desc字段或desc为空');
            }
          } catch (jsonError) {
            console.log(`图文解析JSON模式${i + 1}解析失败:`, jsonError.message);
            continue;
          }
        }
      }
      
      // 如果大JSON解析失败，尝试查找包含desc的小JSON片段
      if (!foundTitle) {
        console.log('图文解析：大JSON解析失败，尝试查找包含desc的JSON片段...');
        const lastPattern = jsonPatterns[jsonPatterns.length - 1];
        const descJsonMatches = html.match(lastPattern);
        if (descJsonMatches && descJsonMatches.length > 0) {
          console.log('图文解析：找到', descJsonMatches.length, '个包含desc的JSON片段');
          for (let i = 0; i < descJsonMatches.length; i++) {
            const jsonStr = descJsonMatches[i];
            try {
              console.log('图文解析：尝试解析JSON片段', i + 1, ':', jsonStr);
              const obj = JSON.parse(jsonStr);
              if (obj.desc && obj.desc.trim() && obj.desc.length > 2) {
                title = obj.desc.trim();
                foundTitle = true;
                console.log('图文解析：从小JSON片段中提取到标题:', title);
                console.log('图文解析：标题完整长度:', title.length);
                break;
              } else {
                console.log('图文解析：JSON片段没有有效的desc字段');
              }
            } catch (e) {
              console.log('图文解析：JSON片段', i + 1, '解析失败:', e.message);
              continue;
            }
          }
        } else {
          console.log('图文解析：没有找到包含desc的JSON片段');
        }
      }
      
    } catch (error) {
      console.log('图文解析JSON过程出错:', error.message);
    }

    // 如果JSON解析失败，尝试从HTML title标签提取
    if (title === '图文内容') {
      console.log('图文解析：JSON解析失败，尝试从HTML title标签提取...');
      const titleTagMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
      if (titleTagMatch) {
        title = titleTagMatch[1].replace(/\s*-\s*抖音$/, '').trim();
        console.log('图文解析：从HTML title标签提取到标题:', title);
      } else {
        console.log('图文解析：HTML title标签也未找到，保持默认标题');
      }
    }
    
    console.log('图文解析：最终提取到标题:', title);

    // 提取作者 - 也使用JSON解析方式
    let author = '未知作者';
    console.log('开始使用JSON解析提取图文作者...');
    try {
      // 查找所有可能的JSON片段中的nickname字段
      const jsonPatterns = [
        /window\._ROUTER_DATA\s*=\s*({.+?})\s*<\/script>/s,
        /window\.__INITIAL_STATE__\s*=\s*({.+?})\s*;/s,
        /__NUXT__\s*=\s*({.+?})\s*;/s
      ];
      
      let foundAuthor = false;
      
      for (let i = 0; i < jsonPatterns.length; i++) {
        const pattern = jsonPatterns[i];
        const match = html.match(pattern);
        if (match && match[1]) {
          try {
            const jsonData = JSON.parse(match[1]);
            
            // 递归查找nickname字段
            const nickname = findFieldInObject(jsonData, 'nickname', ['author', 'user', 'name']);
            if (nickname && nickname.trim()) {
              author = nickname.trim();
              foundAuthor = true;
              console.log('图文解析：从JSON对象中提取到作者:', author);
              break;
            }
          } catch (jsonError) {
            console.log(`图文解析作者JSON模式${i + 1}解析失败:`, jsonError.message);
            continue;
          }
        }
      }
      
      if (!foundAuthor) {
        console.log('图文解析：JSON解析作者失败，保持默认作者');
      }
      
    } catch (error) {
      console.log('图文解析作者JSON过程出错:', error.message);
    }
    console.log('提取到作者:', author);

    // 开始提取图片（已通过aweme_type确定为图文内容）

    // 提取图片URL
    let imageUrls = [];

    // 方法1：专门针对图文视频的图片提取
    console.log('开始提取图文视频的图片...');

    // 方法1：专门提取图文视频的原始图片
    console.log('开始提取图文视频的原始图片...');

    // 先查找images数组的位置
    const imagesArrayIndex = html.indexOf('"images":[');
    if (imagesArrayIndex !== -1) {
      console.log('找到images数组，位置:', imagesArrayIndex);

      // 找到images数组的结束位置
      let bracketCount = 0;
      let startIndex = imagesArrayIndex + '"images":'.length;
      let endIndex = startIndex;

      for (let i = startIndex; i < html.length; i++) {
        if (html[i] === '[') {
          bracketCount++;
        } else if (html[i] === ']') {
          bracketCount--;
          if (bracketCount === 0) {
            endIndex = i;
            break;
          }
        }
      }

      if (endIndex > startIndex) {
        const imagesContent = html.substring(startIndex + 1, endIndex); // 去掉首尾的[]
        console.log('images数组内容长度:', imagesContent.length);
        console.log('images内容片段:', imagesContent.substring(0, 200));

        // 使用更简单的方法提取所有url_list
        const urlListPattern = /"url_list":\s*\[\s*"([^"]+)"/g;
        let match;
        let urlCount = 0;

        while ((match = urlListPattern.exec(imagesContent)) !== null) {
          let url = fixIncompleteImageUrl(match[1]);
          
          if (url && url.startsWith('http') && !imageUrls.includes(url)) {
            imageUrls.push(url);
            urlCount++;
            // 图片提取完成
          }
        }

        // 图片数组提取完成
      }
    } else {
      console.log('未找到images数组');
    }

    // 方法2：如果没有找到images数组，尝试从cover中提取（作为备用）
    if (imageUrls.length === 0) {
      console.log('未找到images数组，尝试从cover提取备用图片...');
      const coverMatch = html.match(/"cover"[^}]*?"url_list":\s*\[\s*"([^"]+)"/);
      if (coverMatch) {
        let url = fixIncompleteImageUrl(coverMatch[1]);
        
        if (url && url.startsWith('http')) {
          imageUrls.push(url);
          console.log('从cover提取到备用图片:', url);
        }
      }
    }

    // 方法1.3：尝试从不同的JSON结构中提取
    if (imageUrls.length <= 1) {
      console.log('尝试从其他JSON结构提取图片...');

      // 查找所有包含图片URL的模式
      const imagePatterns = [
        /"url":\s*"(https?:\/\/[^"]*\.(jpg|jpeg|png|webp)[^"]*)"/gi,
        /"src":\s*"(https?:\/\/[^"]*\.(jpg|jpeg|png|webp)[^"]*)"/gi,
        /"uri":\s*"(https?:\/\/[^"]*\.(jpg|jpeg|png|webp)[^"]*)"/gi
      ];

      const foundUrls = new Set();
      imagePatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          const url = match[1];
          if (url && url.startsWith('http') && !foundUrls.has(url)) {
            foundUrls.add(url);
            console.log('找到额外图片URL:', url);
          }
        }
      });

      if (foundUrls.size > imageUrls.length) {
        imageUrls = Array.from(foundUrls).slice(0, 9);
        console.log('通过额外模式匹配到', imageUrls.length, '张图片');
      }
    }

    // 方法2：从cover提取
    if (imageUrls.length === 0) {
      console.log('尝试从cover提取图片...');
      const coverMatch = html.match(/"cover"[^}]*?"url_list":\s*\[\s*"([^"]+)"/);
      if (coverMatch) {
        let url = fixIncompleteImageUrl(coverMatch[1]);
        
        if (url && url.startsWith('http')) {
          imageUrls.push(url);
          console.log('从cover提取到图片:', url);
        }
      }
    }

    // 去重
    const uniqueUrls = [...new Set(imageUrls)];
    console.log('去重前:', imageUrls.length, '张，去重后:', uniqueUrls.length, '张');
    imageUrls = uniqueUrls;

    if (imageUrls.length === 0) {
      throw new Error('无法提取图片URL');
    }

    // 统一修复所有图片URL
    imageUrls = imageUrls.map(url => fixIncompleteImageUrl(url)).filter(url => url && url.startsWith('http'));
    
    console.log('图文解析成功，去重后提取到', imageUrls.length, '张图片');
    console.log('最终图片URL列表:', imageUrls);

    // 检测动态图文的背景视频 (精简版 - 只保留2个最有效方法)
    let backgroundVideoUrl = null;
    console.log('🎬 检测动态图文背景视频...');
    
    // 方法1：查找video字段中的play_addr (最准确)
    const videoPlayAddrMatch = html.match(/"video"[^}]*?"play_addr"[^}]+?"url_list":\s*\[\s*"([^"]+)"/);
    if (videoPlayAddrMatch) {
      backgroundVideoUrl = videoPlayAddrMatch[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
      console.log('✅ 找到图文背景视频:', backgroundVideoUrl);
    }

    // 方法2：备选 - 查找.mp4文件，排除音频文件  
    if (!backgroundVideoUrl) {
      const mp4VideoMatches = html.match(/https?:\/\/[^"'\s]*\.mp4[^"'\s]*/g);
      if (mp4VideoMatches && mp4VideoMatches.length > 0) {
        const validVideos = mp4VideoMatches.filter(url => 
          !url.includes('watermark') && 
          !url.includes('audio') && 
          !url.includes('no_sound')
        );
        if (validVideos.length > 0) {
          backgroundVideoUrl = validVideos[0];
          console.log('✅ 备选方法找到有效视频:', backgroundVideoUrl);
        }
      }
    }

    // 过滤无效文件
    if (backgroundVideoUrl && (
        backgroundVideoUrl.includes('no_sound_volume_audio_file') || 
        backgroundVideoUrl.includes('audio_file.mp3'))) {
      console.log('⚠️ 过滤无效背景视频:', backgroundVideoUrl);
      backgroundVideoUrl = null;
    }
    
    if (backgroundVideoUrl) {
      console.log('🎉 最终背景视频URL:', backgroundVideoUrl);
    } else {
      console.log('ℹ️ 此图文内容没有背景视频');
    }

    return {
      title,
      author,
      videoUrl: imageUrls[0], // 主图片URL
      imageUrls: imageUrls,   // 所有图片
      backgroundVideoUrl: backgroundVideoUrl, // 背景视频URL (通常为空)
      isImageContent: true,
      duration: 0
    };

  } catch (error) {
    console.error('提取图文信息失败:', error);
    throw error;
  }
}

// 修复不完整的图片URL
function fixIncompleteImageUrl(url) {
  if (!url) {
    return url;
  }
  
  // 先处理转义字符
  url = url.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
  
  // 如果已经是完整的HTTP URL，直接返回
  if (url.startsWith('http')) {
    return url;
  }
  
  // 修复不完整的URL - 所有不完整的URL都需要加前缀
  if (url.startsWith('//')) {
    return 'https:' + url;
  } else {
    // 只要不是完整URL，都添加抖音图片域名前缀
    if (url.includes('~tplv-') || url.includes('aweme-images') || url.length > 10) {
      return 'https://p3-sign.douyinpic.com/' + url;
    } else if (url.includes('douyinvod')) {
      return 'https://' + url;
    } else {
      return 'https://p3-sign.douyinpic.com/' + url;
    }
  }
}

async function getNoWatermarkVideo(originalUrl) {
  try {
    console.log('开始去水印处理:', originalUrl);

    // 核心去水印方法：将 playwm 替换为 play
    if (originalUrl.includes('/playwm/')) {
      const cleanUrl = originalUrl.replace('/playwm/', '/play/');
      console.log('去水印成功:', cleanUrl);
      return cleanUrl;
    }

    // 如果没有 playwm，直接返回原URL
    console.log('URL中没有水印标识，返回原URL');
    return originalUrl;

  } catch (error) {
    console.error('去水印处理失败:', error);
    return originalUrl;
  }
}

// 专门获取视频封面的函数
async function getVideoCover(shareUrl) {
  console.log('开始获取视频封面:', shareUrl);

  try {
    // 第一步：获取真实URL
    const realUrl = await getRealUrl(shareUrl);
    console.log('真实URL:', realUrl);

    // 第二步：获取页面内容
    const pageContent = await getPageContent(realUrl);

    // 第三步：提取封面信息
    const coverInfo = extractCoverInfo(pageContent);

    return {
      title: coverInfo.title,
      author: coverInfo.author,
      coverUrl: coverInfo.coverUrl,
      type: 'cover',
      platform: 'douyin',
      source: '抖音'
    };

  } catch (error) {
    console.error('获取视频封面失败:', error);
    throw error;
  }
}

// 提取封面信息
function extractCoverInfo(html) {
  try {
    console.log('开始提取封面信息...');

    // 提取标题 - 使用JSON解析而不是正则表达式
    let title = '未知标题';
    
    // 方法1：查找并解析包含desc字段的JSON对象
    console.log('开始查找JSON数据中的desc字段...');
    
    try {
      // 查找所有可能的JSON片段
      const jsonPatterns = [
        // 主要的JSON数据结构
        /window\._ROUTER_DATA\s*=\s*({.+?})\s*<\/script>/s,
        /window\.__INITIAL_STATE__\s*=\s*({.+?})\s*;/s,
        /__NUXT__\s*=\s*({.+?})\s*;/s,
        
        // 局部包含aweme或desc的JSON
        /"aweme_detail":\s*({.+?"desc".+?})/s,
        /"aweme":\s*({.+?"desc".+?})/s,
        /"item":\s*({.+?"desc".+?})/s,
        
        // 直接查找包含desc的JSON对象
        /{[^{}]*"desc":\s*"[^"]+"\s*[^{}]*}/g
      ];
      
      let foundTitle = false;
      
      // 尝试从大的JSON结构中提取
      for (let i = 0; i < jsonPatterns.length - 1; i++) {
        const pattern = jsonPatterns[i];
        const match = html.match(pattern);
        if (match && match[1]) {
          try {
            console.log(`尝试JSON模式${i + 1}，找到JSON片段，长度:`, match[1].length);
            const jsonData = JSON.parse(match[1]);
            
            // 递归查找desc字段
            const desc = findFieldInObject(jsonData, 'desc', ['title', 'content', 'text', 'description', 'aweme_desc']);
            if (desc && desc.trim() && desc.length > 2) {
              title = desc.trim();
              foundTitle = true;
              console.log('从JSON对象中提取到标题:', title);
              break;
            }
          } catch (jsonError) {
            console.log(`JSON模式${i + 1}解析失败:`, jsonError.message);
            continue;
          }
        }
      }
      
      // 如果大JSON解析失败，尝试查找包含desc的小JSON片段
      if (!foundTitle) {
        console.log('大JSON解析失败，尝试查找包含desc的JSON片段...');
        const lastPattern = jsonPatterns[jsonPatterns.length - 1];
        const descJsonMatches = html.match(lastPattern);
        if (descJsonMatches && descJsonMatches.length > 0) {
          console.log('找到', descJsonMatches.length, '个包含desc的JSON片段');
          for (let i = 0; i < descJsonMatches.length; i++) {
            const jsonStr = descJsonMatches[i];
            try {
              console.log('尝试解析JSON片段', i + 1, ':', jsonStr.substring(0, 100) + '...');
              const obj = JSON.parse(jsonStr);
              if (obj.desc && obj.desc.trim() && obj.desc.length > 2) {
                title = obj.desc.trim();
                foundTitle = true;
                console.log('从小JSON片段中提取到标题:', title);
                break;
              }
            } catch (e) {
              console.log('JSON片段', i + 1, '解析失败:', e.message);
              continue;
            }
          }
        }
      }
      
    } catch (error) {
      console.log('JSON解析过程出错:', error.message);
    }
    
    // 方法2：如果JSON解析失败，使用title标签作为备选
    if (!title || title === '未知标题' || title.length < 3) {
      console.log('JSON解析失败，尝试title标签...');
      const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
      if (titleMatch) {
        title = titleMatch[1].replace(/\s*-\s*抖音$/, '').trim();
        console.log('从title标签提取到标题:', title);
      }
    }
    
    console.log('最终提取到标题:', title);

    // 提取作者
    const authorMatch = html.match(/"nickname":"([^"]+)"/);
    const author = authorMatch ? authorMatch[1] : '未知作者';
    console.log('提取到作者:', author);

    // 提取封面URL - 使用多种方法
    let coverUrl = null;

    // 方法1：从cover字段提取
    const coverMatch = html.match(/"cover"[^}]*?"url_list":\s*\[\s*"([^"]+)"/);
    if (coverMatch) {
      let url = coverMatch[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
      if (url.startsWith('http')) {
        coverUrl = url;
        console.log('方法1找到封面URL:', coverUrl);
      }
    }

    // 方法2：从origin_cover提取
    if (!coverUrl) {
      const originCoverMatch = html.match(/"origin_cover"[^}]*?"url_list":\s*\[\s*"([^"]+)"/);
      if (originCoverMatch) {
        let url = originCoverMatch[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
        if (url.startsWith('http')) {
          coverUrl = url;
          console.log('方法2找到封面URL:', coverUrl);
        }
      }
    }

    // 方法3：从dynamic_cover提取
    if (!coverUrl) {
      const dynamicCoverMatch = html.match(/"dynamic_cover"[^}]*?"url_list":\s*\[\s*"([^"]+)"/);
      if (dynamicCoverMatch) {
        let url = dynamicCoverMatch[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
        if (url.startsWith('http')) {
          coverUrl = url;
          console.log('方法3找到封面URL:', coverUrl);
        }
      }
    }

    // 方法4：从animated_cover提取
    if (!coverUrl) {
      const animatedCoverMatch = html.match(/"animated_cover"[^}]*?"url_list":\s*\[\s*"([^"]+)"/);
      if (animatedCoverMatch) {
        let url = animatedCoverMatch[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
        if (url.startsWith('http')) {
          coverUrl = url;
          console.log('方法4找到封面URL:', coverUrl);
        }
      }
    }

    // 方法5：通用封面URL匹配
    if (!coverUrl) {
      const coverPatterns = [
        /"cover"[^}]*?"uri":"([^"]+)"/,
        /"cover_url":"([^"]+)"/,
        /"thumbnail":"([^"]+)"/
      ];

      for (const pattern of coverPatterns) {
        const match = html.match(pattern);
        if (match) {
          let url = match[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
          if (url.startsWith('http')) {
            coverUrl = url;
            console.log('方法5找到封面URL:', coverUrl);
            break;
          }
        }
      }
    }

    if (!coverUrl) {
      throw new Error('无法提取封面URL');
    }

    console.log('封面信息提取成功');
    console.log('最终封面URL:', coverUrl);

    return {
      title,
      author,
      coverUrl
    };

  } catch (error) {
    console.error('提取封面信息失败:', error);
    throw error;
  }
}
