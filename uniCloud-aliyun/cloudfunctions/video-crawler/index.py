# -*- coding: utf-8 -*-
import json
import re
import requests
import base64
from urllib.parse import urlparse, parse_qs
import time

def main_handler(event, context):
    """
    视频爬虫云函数 - Python版本
    支持抖音、快手、小红书等平台的视频解析
    """
    print("收到请求:", event)
    
    try:
        link = event.get('link', '')
        if not link:
            return {
                'success': False,
                'message': '链接不能为空'
            }
        
        # 检测平台类型
        platform = detect_platform(link)
        if not platform:
            return {
                'success': False,
                'message': '不支持的平台链接'
            }
        
        # 根据平台解析
        if platform == 'douyin':
            result = parse_douyin(link)
        elif platform == 'kuaishou':
            result = parse_kuaishou(link)
        elif platform == 'xiaohongshu':
            result = parse_xiaohongshu(link)
        else:
            return {
                'success': False,
                'message': f'暂不支持{platform}平台'
            }
        
        return {
            'success': True,
            'data': result
        }
        
    except Exception as e:
        print(f"解析失败: {str(e)}")
        return {
            'success': False,
            'message': f'解析失败: {str(e)}'
        }

def detect_platform(link):
    """检测平台类型"""
    if re.search(r'douyin\.com|dy\.com', link, re.I):
        return 'douyin'
    elif re.search(r'kuaishou\.com|ks\.com', link, re.I):
        return 'kuaishou'
    elif re.search(r'xiaohongshu\.com|xhslink\.com', link, re.I):
        return 'xiaohongshu'
    return None

def parse_douyin(share_url):
    """解析抖音视频"""
    print(f"开始解析抖音链接: {share_url}")
    
    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
    }
    
    try:
        # 第一步：获取真实URL
        response = requests.get(share_url, headers=headers, allow_redirects=False, timeout=10)
        real_url = response.headers.get('Location', share_url)
        print(f"真实URL: {real_url}")
        
        # 第二步：获取页面内容
        response = requests.get(real_url, headers=headers, timeout=15)
        html_content = response.text
        
        # 第三步：提取视频信息
        video_info = extract_douyin_info(html_content)
        
        # 第四步：下载并处理视频
        processed_video = download_and_process_video(video_info['video_url'], headers)
        
        return {
            'title': video_info['title'],
            'author': video_info['author'],
            'processedData': {
                'data': processed_video,
                'type': 'video/mp4',
                'size': len(base64.b64decode(processed_video)) if processed_video else 0
            },
            'type': 'video',
            'platform': 'douyin',
            'source': '抖音'
        }
        
    except Exception as e:
        print(f"抖音解析失败: {str(e)}")
        raise e

def extract_douyin_info(html_content):
    """从HTML中提取抖音视频信息"""
    try:
        # 提取标题
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.I)
        title = title_match.group(1).replace(' - 抖音', '').strip() if title_match else '未知标题'

        # 提取作者
        author_match = re.search(r'"nickname":"([^"]+)"', html_content)
        author = author_match.group(1) if author_match else '未知作者'

        # 方法一：提取无水印视频URL
        # 抖音有多个视频URL，其中某些是无水印的
        video_urls = []

        # 提取所有可能的视频URL
        play_addr_matches = re.findall(r'"play_addr":\{"url_list":\[([^\]]+)\]', html_content)
        for match in play_addr_matches:
            urls = re.findall(r'"([^"]+)"', match)
            video_urls.extend(urls)

        # 清理URL
        clean_urls = []
        for url in video_urls:
            clean_url = url.replace('\\u002F', '/').replace('\\', '')
            if clean_url.startswith('http'):
                clean_urls.append(clean_url)

        if not clean_urls:
            raise Exception("无法提取视频URL")

        # 选择最佳URL（通常第一个是高质量的）
        video_url = clean_urls[0]

        # 尝试获取无水印版本
        # 抖音的无水印URL通常可以通过替换参数获得
        no_watermark_url = get_no_watermark_url(video_url)

        return {
            'title': title,
            'author': author,
            'video_url': no_watermark_url or video_url,
            'original_url': video_url,
            'is_watermark_free': no_watermark_url is not None
        }

    except Exception as e:
        print(f"提取视频信息失败: {str(e)}")
        raise e

def get_no_watermark_url(video_url):
    """尝试获取无水印视频URL"""
    try:
        # 抖音的无水印视频可以通过修改URL参数获得
        # 这是一个常用的技巧，但可能会失效

        # 方法1：替换watermark参数
        if 'watermark=1' in video_url:
            return video_url.replace('watermark=1', 'watermark=0')

        # 方法2：添加无水印参数
        if '?' in video_url:
            return video_url + '&watermark=0'
        else:
            return video_url + '?watermark=0'

    except Exception as e:
        print(f"获取无水印URL失败: {e}")
        return None

def download_and_process_video(video_url, headers):
    """下载并处理视频（去水印）"""
    try:
        print(f"开始下载视频: {video_url}")
        
        # 下载视频
        video_headers = headers.copy()
        video_headers['Referer'] = 'https://www.douyin.com/'
        
        response = requests.get(video_url, headers=video_headers, timeout=30)
        video_data = response.content
        
        print(f"视频下载完成，大小: {len(video_data)} bytes")
        
        # 这里可以添加视频处理逻辑
        # 例如：使用 OpenCV 或 FFmpeg 去除水印
        processed_data = process_video_remove_watermark(video_data)
        
        # 转换为base64
        base64_data = base64.b64encode(processed_data).decode('utf-8')
        
        return base64_data
        
    except Exception as e:
        print(f"视频下载处理失败: {str(e)}")
        raise e

def process_video_remove_watermark(video_data):
    """视频去水印处理"""
    print("执行去水印处理...")

    # 方案一：使用FFmpeg命令行工具
    # 这需要在云函数环境中安装FFmpeg
    try:
        processed_data = remove_watermark_with_ffmpeg(video_data)
        return processed_data
    except Exception as e:
        print(f"FFmpeg处理失败: {e}")

    # 方案二：使用OpenCV进行帧处理
    try:
        processed_data = remove_watermark_with_opencv(video_data)
        return processed_data
    except Exception as e:
        print(f"OpenCV处理失败: {e}")

    # 方案三：简单的视频URL替换（抖音特有）
    # 抖音的无水印视频可以通过URL参数获取
    print("使用URL替换方案...")
    return video_data

def remove_watermark_with_ffmpeg(video_data):
    """使用FFmpeg去除水印"""
    import subprocess
    import tempfile
    import os

    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as input_file:
        input_file.write(video_data)
        input_path = input_file.name

    output_path = input_path.replace('.mp4', '_no_watermark.mp4')

    try:
        # FFmpeg命令：去除右下角水印（抖音水印通常在这里）
        cmd = [
            'ffmpeg',
            '-i', input_path,
            '-vf', 'delogo=x=W-200:y=H-100:w=180:h=80',  # 去除右下角水印
            '-c:a', 'copy',  # 音频不处理
            output_path
        ]

        subprocess.run(cmd, check=True, capture_output=True)

        # 读取处理后的视频
        with open(output_path, 'rb') as f:
            processed_data = f.read()

        return processed_data

    finally:
        # 清理临时文件
        if os.path.exists(input_path):
            os.remove(input_path)
        if os.path.exists(output_path):
            os.remove(output_path)

def remove_watermark_with_opencv(video_data):
    """使用OpenCV去除水印"""
    import cv2
    import numpy as np
    import tempfile
    import os

    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as input_file:
        input_file.write(video_data)
        input_path = input_file.name

    output_path = input_path.replace('.mp4', '_no_watermark.mp4')

    try:
        # 打开视频
        cap = cv2.VideoCapture(input_path)

        # 获取视频属性
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # 去除水印：在水印区域填充周围像素
            # 抖音水印通常在右下角
            watermark_region = frame[height-100:height, width-200:width]

            # 使用图像修复技术去除水印
            mask = np.zeros((100, 200), dtype=np.uint8)
            mask.fill(255)  # 白色表示需要修复的区域

            # 使用cv2.inpaint进行图像修复
            repaired_region = cv2.inpaint(watermark_region, mask, 3, cv2.INPAINT_TELEA)

            # 将修复后的区域放回原图
            frame[height-100:height, width-200:width] = repaired_region

            out.write(frame)

        cap.release()
        out.release()

        # 读取处理后的视频
        with open(output_path, 'rb') as f:
            processed_data = f.read()

        return processed_data

    finally:
        # 清理临时文件
        if os.path.exists(input_path):
            os.remove(input_path)
        if os.path.exists(output_path):
            os.remove(output_path)

def parse_kuaishou(share_url):
    """解析快手视频"""
    # TODO: 实现快手解析逻辑
    raise Exception("快手解析功能开发中")

def parse_xiaohongshu(share_url):
    """解析小红书内容"""
    print(f"开始解析小红书链接: {share_url}")

    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://www.xiaohongshu.com/',
        'Connection': 'keep-alive'
    }

    try:
        # 第一步：获取真实URL
        real_url = get_real_url_xiaohongshu(share_url, headers)
        print(f"真实URL: {real_url}")

        # 第二步：获取页面内容
        response = requests.get(real_url, headers=headers, timeout=15)
        html = response.text
        print(f"页面内容长度: {len(html)}")

        # 第三步：解析内容
        content_info = parse_xiaohongshu_content(html, real_url)

        return content_info

    except Exception as e:
        print(f"小红书解析失败: {str(e)}")
        raise e

def get_real_url_xiaohongshu(share_url, headers):
    """获取小红书真实URL"""
    try:
        response = requests.get(share_url, headers=headers, allow_redirects=True, timeout=10)
        return response.url
    except Exception as e:
        print(f"获取真实URL失败: {str(e)}")
        return share_url

def parse_xiaohongshu_content(html, url):
    """解析小红书页面内容"""
    import json
    import re

    try:
        # 提取基本信息
        title = "小红书内容"
        author = "未知作者"
        content_type = "unknown"

        # 方法1：从window.__INITIAL_STATE__中提取
        initial_state_match = re.search(r'window\.__INITIAL_STATE__\s*=\s*({.+?});', html)
        if initial_state_match:
            try:
                initial_state = json.loads(initial_state_match.group(1))
                print("找到初始状态数据")

                # 查找笔记数据
                note_data = find_note_data_xiaohongshu(initial_state)
                if note_data:
                    return parse_note_data_xiaohongshu(note_data)
            except Exception as e:
                print(f"解析初始状态失败: {str(e)}")

        # 方法2：从页面标题提取
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', html, re.I)
        if title_match:
            title = re.sub(r'\s*-\s*小红书.*$', '', title_match.group(1)).strip()

        # 方法3：从script标签中查找数据
        script_matches = re.findall(r'<script[^>]*>([\s\S]*?)</script>', html, re.I)
        for script in script_matches:
            json_match = re.search(r'({[\s\S]*"noteId"[\s\S]*?})', script)
            if json_match:
                try:
                    data = json.loads(json_match.group(1))
                    note_data = find_note_data_in_object_xiaohongshu(data)
                    if note_data:
                        return parse_note_data_xiaohongshu(note_data)
                except:
                    continue

        # 如果都没找到，返回基本信息
        return {
            'title': title,
            'author': author,
            'content_type': content_type,
            'message': '无法获取详细内容信息'
        }

    except Exception as e:
        print(f"解析页面内容失败: {str(e)}")
        raise e

def find_note_data_xiaohongshu(obj, path=''):
    """在初始状态中查找笔记数据"""
    if not isinstance(obj, dict):
        return None

    # 检查当前对象是否包含笔记数据
    if 'noteId' in obj or 'note_id' in obj:
        print(f"找到笔记数据: {path}")
        return obj

    # 递归查找
    for key, value in obj.items():
        if isinstance(value, dict):
            result = find_note_data_xiaohongshu(value, f"{path}.{key}")
            if result:
                return result

    return None

def find_note_data_in_object_xiaohongshu(obj):
    """在对象中查找笔记数据"""
    if not isinstance(obj, dict):
        return None

    # 常见的笔记数据字段
    note_fields = ['noteId', 'note_id', 'id', 'title', 'desc', 'video', 'imageList']

    if any(field in obj for field in note_fields):
        return obj

    # 递归查找
    for value in obj.values():
        if isinstance(value, dict):
            result = find_note_data_in_object_xiaohongshu(value)
            if result:
                return result

    return None

def parse_note_data_xiaohongshu(note_data):
    """解析笔记数据"""
    try:
        print(f"解析笔记数据: {list(note_data.keys())}")

        result = {
            'title': note_data.get('title') or note_data.get('desc') or '小红书内容',
            'author': (note_data.get('user', {}).get('nickname') or
                      note_data.get('author', {}).get('nickname') or '未知作者'),
            'content_type': 'unknown',
            'duration': 0
        }

        # 判断内容类型并处理
        if note_data.get('video') or note_data.get('videoUrl'):
            # 视频内容
            result['content_type'] = 'video'
            video_url = (note_data.get('video', {}).get('url') or
                        note_data.get('videoUrl') or
                        note_data.get('video', {}).get('playUrl'))

            if video_url:
                result['video_url'] = process_video_url_xiaohongshu(video_url)
                result['duration'] = note_data.get('video', {}).get('duration', 0)

        elif note_data.get('imageList') or note_data.get('images'):
            # 图文内容
            result['content_type'] = 'image'
            images = note_data.get('imageList') or note_data.get('images') or []
            image_urls = []
            for img in images:
                if isinstance(img, dict):
                    url = img.get('url') or img.get('src')
                else:
                    url = str(img)
                if url:
                    image_urls.append(url)

            result['image_urls'] = image_urls
            result['video_url'] = image_urls[0] if image_urls else None  # 主图片作为预览
            result['is_image_content'] = True

        print(f"解析结果: {result}")
        return result

    except Exception as e:
        print(f"解析笔记数据失败: {str(e)}")
        raise e

def process_video_url_xiaohongshu(video_url):
    """处理小红书视频URL（去水印）"""
    try:
        print(f"处理视频URL: {video_url}")

        # 小红书视频去水印处理
        clean_url = video_url

        # 移除水印相关参数
        import re
        clean_url = re.sub(r'[?&]watermark=[^&]*', '', clean_url)
        clean_url = re.sub(r'[?&]wm=[^&]*', '', clean_url)

        # 确保URL格式正确
        if not clean_url.startswith('http'):
            clean_url = 'https:' + clean_url

        print(f"处理后的视频URL: {clean_url}")
        return clean_url

    except Exception as e:
        print(f"处理视频URL失败: {str(e)}")
        return video_url

# uniCloud Python云函数入口
def main(event, context):
    """
    uniCloud Python云函数主入口
    """
    return main_handler(event, context)

def main_handler(event, context):
