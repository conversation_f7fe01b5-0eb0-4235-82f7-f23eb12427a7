# 多图片选择功能测试指南

## 测试环境准备

### 测试数据
需要准备包含多张图片的抖音图文内容链接，建议：
- 3-5张图片的图文内容（测试基本功能）
- 6-9张图片的图文内容（测试大量图片场景）

### 测试设备
- 微信小程序开发工具
- 真机测试（iOS/Android）
- 不同屏幕尺寸的设备

## 功能测试用例

### 1. 基础选择功能测试

#### 测试步骤
1. 解析一个包含多张图片的抖音链接
2. 进入结果页面，确认显示多张图片
3. 点击"保存到相册"按钮
4. 观察是否弹出选择方式菜单

#### 预期结果
```
选择保存方式 (共X张图片)
- 🖼️ 可视化选择
- ✅ 全部保存  
- 1️⃣ 仅保存第一张
- 🔢 输入序号选择
```

### 2. 可视化选择器测试

#### 测试步骤
1. 选择"🖼️ 可视化选择"
2. 观察图片选择器界面
3. 测试以下操作：
   - 点击单张图片切换选择状态
   - 点击"全选"/"取消全选"按钮
   - 观察选择计数变化
   - 点击"取消"按钮
   - 重新打开，点击"保存选中的图片"

#### 预期结果
- **界面显示**: 3列网格布局，显示所有图片
- **选择状态**: 默认全选，点击可切换
- **计数显示**: "已选择 X 张"实时更新
- **按钮状态**: 未选择时"保存"按钮禁用
- **操作反馈**: 选中图片有蓝色遮罩和勾选标记

### 3. 全部保存测试

#### 测试步骤
1. 选择"✅ 全部保存"
2. 观察保存进度
3. 检查相册中的图片

#### 预期结果
- 显示保存进度："正在保存图片 X/Y"
- 所有图片都保存到相册
- 显示成功提示："成功保存X张图片"

### 4. 仅保存第一张测试

#### 测试步骤
1. 选择"1️⃣ 仅保存第一张"
2. 观察保存过程
3. 检查相册

#### 预期结果
- 只保存第一张图片
- 显示："保存成功"

### 5. 输入序号选择测试

#### 测试步骤
1. 选择"🔢 输入序号选择"
2. 测试不同输入：
   - 正确格式："1,3,5"
   - 错误格式："abc"
   - 超出范围："1,10"（假设只有5张图片）
   - 重复数字："1,1,3"
   - 空输入：""

#### 预期结果
- **正确输入**: 保存对应序号的图片
- **错误格式**: 显示"输入格式错误"
- **超出范围**: 自动过滤无效序号
- **重复数字**: 自动去重
- **空输入**: 取消操作

### 6. 错误处理测试

#### 网络错误测试
1. 断开网络连接
2. 尝试保存图片
3. 观察错误提示

#### 权限错误测试
1. 拒绝相册权限
2. 尝试保存图片
3. 观察权限引导

#### 图片加载失败测试
1. 使用无效的图片URL（需要修改测试数据）
2. 观察选择器中的错误显示

## 性能测试

### 大量图片测试
- 测试9张图片的选择和保存
- 观察界面响应速度
- 检查内存使用情况

### 网络环境测试
- WiFi环境下的保存速度
- 4G环境下的保存速度
- 弱网环境下的表现

## 兼容性测试

### 不同设备测试
- iPhone（不同尺寸）
- Android（不同品牌）
- 平板设备

### 不同微信版本
- 最新版微信
- 较旧版本微信

## 用户体验测试

### 易用性测试
1. **首次使用**: 用户能否快速理解各选项含义
2. **操作流畅性**: 选择过程是否流畅自然
3. **反馈及时性**: 操作反馈是否及时清晰

### 界面测试
1. **布局适配**: 不同屏幕尺寸下的显示效果
2. **图片质量**: 缩略图是否清晰
3. **交互反馈**: 点击、选择的视觉反馈

## 边界条件测试

### 极端情况
- **单张图片**: 应直接保存，不显示选择界面
- **大量图片**: 测试20+张图片的处理
- **超大图片**: 测试高分辨率图片的处理
- **网络超时**: 长时间网络请求的处理

### 异常操作
- **快速点击**: 连续快速点击按钮
- **中途取消**: 保存过程中退出应用
- **内存不足**: 设备存储空间不足时的处理

## 测试检查清单

### 功能完整性 ✓
- [ ] 选择方式菜单正常显示
- [ ] 可视化选择器正常工作
- [ ] 全部保存功能正常
- [ ] 单张保存功能正常
- [ ] 序号输入选择正常
- [ ] 图片保存到相册成功

### 用户体验 ✓
- [ ] 界面布局美观
- [ ] 操作流程顺畅
- [ ] 错误提示友好
- [ ] 加载状态清晰
- [ ] 成功反馈及时

### 错误处理 ✓
- [ ] 网络错误处理
- [ ] 权限错误处理
- [ ] 输入验证正确
- [ ] 异常情况降级

### 性能表现 ✓
- [ ] 界面响应及时
- [ ] 内存使用合理
- [ ] 保存速度可接受
- [ ] 不同网络环境适应

## 回归测试

每次修改后需要验证：
1. 原有的单张图片保存功能
2. 原有的视频保存功能
3. 新增的多图片选择功能
4. 整体应用稳定性
