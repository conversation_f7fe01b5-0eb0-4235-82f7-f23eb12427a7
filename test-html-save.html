<!DOCTYPE html>
<html>
<head>
    <title>测试小红书HTML保存功能</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9; }
        .loading { color: #666; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        .html-link { margin-top: 10px; }
        .html-link a { color: #007bff; text-decoration: none; }
        .html-link a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>小红书HTML源码保存测试</h1>
        <p>这个工具可以获取小红书页面的HTML源码并保存到云存储，方便分析页面结构。</p>
        
        <div class="form-group">
            <label for="linkInput">小红书链接:</label>
            <input type="text" id="linkInput" placeholder="请输入小红书链接，例如: http://xhslink.com/m/8PU8raeBZFj" 
                   value="http://xhslink.com/m/8PU8raeBZFj">
        </div>
        
        <button onclick="saveHtml()">获取并保存HTML源码</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        async function saveHtml() {
            const link = document.getElementById('linkInput').value.trim();
            const resultDiv = document.getElementById('result');
            
            if (!link) {
                alert('请输入小红书链接');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading">正在获取HTML源码...</div>';
            
            try {
                const response = await uniCloud.callFunction({
                    name: 'xiaohongshu-parser',
                    data: {
                        link: link,
                        saveHtml: true  // 启用HTML保存功能
                    }
                });
                
                console.log('解析结果:', response);
                
                if (response.result.success) {
                    const data = response.result.data;
                    let html = '<div class="success">✅ HTML源码获取成功!</div>';
                    html += '<h3>基本信息:</h3>';
                    html += '<p><strong>标题:</strong> ' + (data.title || '未知') + '</p>';
                    html += '<p><strong>作者:</strong> ' + (data.author || '未知') + '</p>';
                    html += '<p><strong>类型:</strong> ' + (data.type || '未知') + '</p>';
                    html += '<p><strong>Note ID:</strong> ' + (data.noteId || '未知') + '</p>';
                    
                    if (data.htmlFile) {
                        html += '<div class="html-link">';
                        html += '<h3>HTML源码文件:</h3>';
                        html += '<p><strong>文件名:</strong> ' + data.htmlFile.fileName + '</p>';
                        if (data.htmlFile.downloadUrl) {
                            html += '<p><strong>下载链接:</strong> <a href="' + data.htmlFile.downloadUrl + '" target="_blank">点击下载HTML源码</a></p>';
                            html += '<p><small>注意: 下载链接有效期为1小时</small></p>';
                        }
                        html += '</div>';
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ 解析失败: ' + (response.result.message || '未知错误') + '</div>';
                }
                
            } catch (error) {
                console.error('请求失败:', error);
                resultDiv.innerHTML = '<div class="error">❌ 请求失败: ' + error.message + '</div>';
            }
        }
        
        // 回车键提交
        document.getElementById('linkInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                saveHtml();
            }
        });
    </script>
</body>
</html>
