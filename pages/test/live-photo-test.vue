<template>
  <view class="container">
    <view class="header">
      <text class="title">Live Photo 功能测试</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">测试链接</text>
      <textarea 
        v-model="testLink" 
        placeholder="粘贴包含Live Photo的小红书链接"
        class="test-input"
      />
      <button @click="testLivePhoto" class="test-btn" :disabled="!testLink">
        测试解析
      </button>
    </view>
    
    <view class="result-section" v-if="testResult">
      <text class="section-title">解析结果</text>
      <view class="result-info">
        <text class="info-text">标题: {{ testResult.title }}</text>
        <text class="info-text">类型: {{ testResult.type }}</text>
        <text class="info-text">图片数量: {{ testResult.processedData?.imageUrls?.length || 0 }}</text>
        <text class="info-text">Live Photo数量: {{ testResult.processedData?.videoUrls?.length || 0 }}</text>
      </view>
      
      <!-- 显示Live Photo列表 -->
      <view v-if="testResult.processedData?.videoUrls?.length > 0" class="live-photo-list">
        <text class="list-title">Live Photo 视频列表:</text>
        <view class="video-item" v-for="(videoUrl, index) in testResult.processedData.videoUrls" :key="index">
          <text class="video-index">{{ index + 1 }}.</text>
          <text class="video-url">{{ videoUrl.substring(0, 80) }}...</text>
          <button @click="copyUrl(videoUrl)" class="copy-btn">复制</button>
        </view>
      </view>
    </view>
    
    <view class="debug-section" v-if="debugLogs.length > 0">
      <text class="section-title">调试日志</text>
      <scroll-view class="debug-logs" scroll-y>
        <text class="debug-log" v-for="(log, index) in debugLogs" :key="index">
          {{ log }}
        </text>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      testLink: '',
      testResult: null,
      debugLogs: []
    }
  },
  
  methods: {
    async testLivePhoto() {
      if (!this.testLink.trim()) return
      
      this.testResult = null
      this.debugLogs = []
      
      uni.showLoading({
        title: '解析中...'
      })
      
      try {
        const result = await uniCloud.callFunction({
          name: 'xiaohongshu-parser',
          data: {
            link: this.testLink.trim(),
            debug: true
          }
        })
        
        console.log('解析结果:', result)
        
        if (result.result?.success) {
          this.testResult = result.result.data
          this.debugLogs = result.result.debugLogs || []
        } else {
          uni.showToast({
            title: result.result?.message || '解析失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('测试失败:', error)
        uni.showToast({
          title: '测试失败: ' + error.message,
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },
    
    copyUrl(url) {
      uni.setClipboardData({
        data: url,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success'
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20rpx;
  min-height: 100vh;
  background: #F5F5F5;
}

.header {
  text-align: center;
  padding: 40rpx 0;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section, .result-section, .debug-section {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.test-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #E5E5E5;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 30rpx;
}

.test-btn:disabled {
  background: #CCCCCC;
}

.result-info {
  margin-bottom: 20rpx;
}

.info-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.list-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.live-photo-list {
  margin-top: 20rpx;
}

.video-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.video-index {
  font-size: 26rpx;
  color: #666;
  width: 60rpx;
  flex-shrink: 0;
}

.video-url {
  font-size: 24rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
  margin-right: 20rpx;
}

.copy-btn {
  padding: 10rpx 20rpx;
  background: #F0F8FF;
  color: #007AFF;
  border: 1rpx solid #007AFF;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.debug-logs {
  max-height: 400rpx;
  background: #F8F8F8;
  border-radius: 8rpx;
  padding: 20rpx;
}

.debug-log {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
  font-family: monospace;
  word-break: break-all;
}
</style>
